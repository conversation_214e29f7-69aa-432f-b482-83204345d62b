/** 
*    ===============================================================================
*    MCP Tools Connector for Xiaozhi AI Assistant - WebSocket Client Version
*   ===============================================================================
*
*
*    ===============================================================================
*/
#ifndef REMOTE_WAKE_H
#define REMOTE_WAKE_H

#include "application.h"
#include <string>
#include <web_socket.h>
#include <freertos/FreeRTOS.h>
#include <freertos/event_groups.h>

namespace remote_wake {

/**
 * 远程唤醒WebSocket客户端
 * 主动连接到公网服务器，接收远程指令
 */
class RemoteWakeClient {
public:
    RemoteWakeClient();
    ~RemoteWakeClient();

    /**
     * 启动WebSocket客户端并连接服务器
     */
    void Start();

    /**
     * 停止WebSocket客户端
     */
    void Stop();

    /**
     * 获取单例实例
     */
    static RemoteWakeClient* GetInstance() { return instance_; }

    /**
     * 设置服务器URL
     * @param server_url 服务器WebSocket地址
     */
    void SetServerUrl(const std::string& server_url);

    /**
     * 获取当前连接状态
     * @return 是否已连接
     */
    bool IsConnected() const { return connected_; }

    /**
     * 发送消息到服务器
     * @param event_name 事件名称
     * @param data JSON格式的数据
     * @return 是否发送成功
     */
    bool SendMessage(const std::string& event_name, const std::string& data);

    /**
     * 注册设备到服务器
     * @param device_id 设备ID
     * @param device_name 设备名称
     * @return 是否注册成功
     */
    bool RegisterDevice(const std::string& device_id, const std::string& device_name);

    /**
     * 发送设备状态到服务器
     * @param status 设备状态
     */
    void SendDeviceStatus(const std::string& status);

private:
    WebSocket* client_;
    static RemoteWakeClient* instance_;
    std::string server_url_;
    std::string device_id_;
    std::string device_name_;
    bool connected_;
    bool registered_;
    EventGroupHandle_t event_group_handle_;

    /**
     * 处理接收到的WebSocket数据
     */
    void HandleWebSocketData(const char* data, size_t len, bool binary);

    /**
     * 处理WebSocket断开连接
     */
    void HandleWebSocketDisconnected();

    /**
     * 处理连接事件
     */
    void OnConnected();

    /**
     * 处理断连事件
     */
    void OnDisconnected();

    /**
     * 解析WebSocket JSON消息格式
     * @param raw_message 原始消息
     * @param event_name 输出事件名
     * @param event_data 输出事件数据
     * @return 是否解析成功
     */
    bool ParseWebSocketMessage(const std::string& raw_message, std::string& event_name, std::string& event_data);

    /**
     * 构造WebSocket JSON消息格式
     * @param event_name 事件名
     * @param data 数据
     * @return JSON格式的消息
     */
    std::string BuildWebSocketMessage(const std::string& event_name, const std::string& data);

    /**
     * 处理说话指令
     * @param message 要说的内容
     * @param force 是否强制说话
     */
    void HandleSpeakMessage(const std::string& message, bool force = false);

    /**
     * 处理设备唤醒指令
     * @param message 唤醒后要说的内容
     * @param force 是否强制唤醒
     */
    void HandleWakeDevice(const std::string& message, bool force = false);

    /**
     * 处理设备间通信指令
     * @param target_device 目标设备
     * @param message 消息内容
     * @param force 是否强制发送
     */
    void HandleDeviceCommunication(const std::string& target_device, const std::string& message, bool force = false);

    /**
     * 获取设备当前状态JSON
     * @return JSON格式的设备状态
     */
    std::string GetDeviceStatusJson();

    /**
     * 将设备状态枚举转换为字符串
     * @param state 设备状态枚举
     * @return 状态字符串
     */
    std::string GetDeviceStateString(DeviceState state);

    /**
     * 自动发送设备状态更新到服务器
     */
    void SendDeviceStatusUpdate();

    /**
     * 注册设备间通信MCP工具
     */
    void RegisterDeviceCommunicationTools();

    /**
     * 发送消息到指定设备（通过服务器中转）
     * @param target_device 目标设备名称
     * @param message 消息内容
     * @param force 是否强制发送
     * @return JSON格式的响应
     */
    std::string SendMessageToDevice(const std::string& target_device, const std::string& message, bool force);

    /**
     * 发现网络中的设备（通过服务器查询）
     * @return JSON格式的设备列表
     */
    std::string DiscoverDevices();

    /**
     * 获取远程设备状态（通过服务器查询）
     * @param target_device 目标设备名称
     * @return JSON格式的设备状态
     */
    std::string GetRemoteDeviceStatus(const std::string& target_device);

    /**
     * 发送心跳包
     */
    void SendHeartbeat();

    /**
     * 心跳定时器任务
     */
    static void HeartbeatTask(void* arg);

    // 心跳任务句柄
    TaskHandle_t heartbeat_task_;
};

} // namespace remote_wake

#endif // REMOTE_WAKE_H