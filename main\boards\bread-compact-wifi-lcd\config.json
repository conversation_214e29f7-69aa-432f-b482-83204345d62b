{"board_name": "bread-compact-wifi-lcd", "display_name": "面包板紧凑型WiFi LCD显示屏", "description": "基于 ESP32-S3 的紧凑型开发板，配备 240x320 LCD 显示屏和 WiFi 连接能力", "features": ["ESP32-S3 双核处理器", "240x320 像素 LCD 彩色显示屏", "WiFi 无线连接", "音频输入输出支持", "MCP 远程唤醒支持", "设备间通信功能"], "capabilities": {"display": true, "audio": true, "wifi": true, "mcp_wake": true, "device_communication": true}, "hardware": {"mcu": "ESP32-S3", "display": {"type": "LCD", "resolution": "240x320", "driver": "ST7789/ST7796/ILI9341"}, "audio": {"input": "I2S 麦克风", "output": "I2S 扬声器", "sample_rate": {"input": 16000, "output": 24000}}, "connectivity": ["WiFi 2.4GHz"]}, "gpio_mapping": {"audio": {"mic_ws": 4, "mic_sck": 5, "mic_din": 6, "spk_dout": 7, "spk_bclk": 15, "spk_lrck": 16}, "display": {"backlight": 42, "mosi": 47, "clk": 21, "dc": 40, "rst": 45, "cs": 41}, "controls": {"builtin_led": 48, "boot_button": 0}, "test": {"lamp_gpio": 18}}, "mcp_features": {"remote_wake": {"enabled": true, "websocket_client": true, "device_registration": true, "status_reporting": true}, "device_communication": {"enabled": true, "server_relay": true, "message_forwarding": true, "device_discovery": true}, "supported_commands": ["speak_message", "wake_device", "device_communication", "device_status_request", "reconnect_server", "reboot_device"]}, "configuration": {"audio_method": "simplex", "display_config": "multiple LCD types supported via Kconfig", "websocket_config": "managed by websocket_config module"}}