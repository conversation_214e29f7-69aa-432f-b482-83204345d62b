#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP32 WebSocket客户端模拟器
模拟ESP32设备的原始WebSocket连接方式
"""

import websocket
import json
import time
import threading

class ESP32WebSocketClient:
    def __init__(self, server_url="ws://127.0.0.1:5001"):
        self.server_url = server_url
        self.ws = None
        self.connected = False
        self.device_info = {
            "device_id": "30:ed:a0:23:82:ec",
            "device_name": "小灵16号",
            "device_type": "esp32",
            "capabilities": "voice,display"
        }
    
    def on_message(self, ws, message):
        """收到服务器消息"""
        print(f"📨 收到服务器消息: {message}")
        
        # 检查是否是注册响应
        if '"device_register_response"' in message:
            print("✅ 收到设备注册响应！")
        
        # 处理Socket.IO握手
        if message.startswith('0{'):
            print("🤝 收到Socket.IO握手响应")
        elif message == '2':
            print("💓 收到心跳ping，发送pong")
            ws.send('3')
        elif message == '6':
            print("🔄 收到升级确认")
    
    def on_error(self, ws, error):
        """连接错误"""
        print(f"❌ WebSocket错误: {error}")
    
    def on_close(self, ws, close_status_code, close_msg):
        """连接关闭"""
        print(f"🔌 WebSocket连接关闭: {close_status_code}, {close_msg}")
        self.connected = False
    
    def on_open(self, ws):
        """连接建立"""
        print("✅ WebSocket连接建立")
        self.connected = True
        
        # 发送Socket.IO握手
        print("🤝 发送Socket.IO握手...")
        # 这里不需要发送握手，直接发送注册消息
        
        # 等待一下再发送注册消息
        time.sleep(1)
        self.send_device_register()
    
    def send_device_register(self):
        """发送设备注册消息"""
        if not self.connected:
            print("❌ 连接未建立，无法发送注册消息")
            return
        
        # 构造ESP32格式的Socket.IO消息
        register_message = f'42["device_register",{json.dumps(self.device_info, ensure_ascii=False)}]'
        
        print(f"📱 发送设备注册消息:")
        print(f"📤 消息内容: {register_message}")
        
        self.ws.send(register_message)
        print("✅ 注册消息已发送")
    
    def send_device_status(self, status="idle"):
        """发送设备状态"""
        if not self.connected:
            print("❌ 连接未建立，无法发送状态")
            return
        
        status_data = {
            "device_id": self.device_info["device_id"],
            "device_name": self.device_info["device_name"],
            "device_state": status,
            "timestamp": int(time.time())
        }
        
        status_message = f'42["device_status",{json.dumps(status_data, ensure_ascii=False)}]'
        
        print(f"📊 发送设备状态: {status}")
        print(f"📤 消息内容: {status_message}")
        
        self.ws.send(status_message)
    
    def connect(self):
        """连接到服务器"""
        print(f"🔗 连接到服务器: {self.server_url}")
        
        self.ws = websocket.WebSocketApp(
            self.server_url,
            on_open=self.on_open,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close
        )
        
        # 在新线程中运行WebSocket
        self.ws.run_forever()
    
    def disconnect(self):
        """断开连接"""
        if self.ws:
            self.ws.close()

def test_esp32_websocket():
    """测试ESP32 WebSocket连接"""
    print("🧪 ESP32 WebSocket客户端测试")
    print("=" * 50)
    
    client = ESP32WebSocketClient()
    
    try:
        # 在新线程中连接
        connect_thread = threading.Thread(target=client.connect)
        connect_thread.daemon = True
        connect_thread.start()
        
        # 等待连接建立
        time.sleep(2)
        
        if client.connected:
            print("✅ 连接成功，等待注册响应...")
            time.sleep(3)
            
            # 发送状态更新
            client.send_device_status("speaking")
            time.sleep(1)
            client.send_device_status("idle")
            
            # 保持连接
            print("⏳ 保持连接10秒...")
            time.sleep(10)
        else:
            print("❌ 连接失败")
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断")
    finally:
        client.disconnect()
        print("🔌 连接已断开")

if __name__ == "__main__":
    test_esp32_websocket()
