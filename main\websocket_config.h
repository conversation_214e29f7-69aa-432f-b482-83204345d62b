#ifndef WEBSOCKET_CONFIG_H
#define WEBSOCKET_CONFIG_H

#include <string>

namespace websocket_config {

/**
 * WebSocket配置管理类
 * 提供WebSocket服务器连接配置和相关参数
 */
class WebSocketConfig {
public:
    /**
     * 获取单例实例
     */
    static WebSocketConfig& GetInstance();

    /**
     * 获取WebSocket服务器URL
     * @return 服务器URL
     */
    std::string GetServerUrl() const;

    /**
     * 设置WebSocket服务器URL
     * @param url 服务器URL
     */
    void SetServerUrl(const std::string& url);

    /**
     * 获取重连超时时间（毫秒）
     */
    uint32_t GetReconnectTimeoutMs() const { return reconnect_timeout_ms_; }

    /**
     * 获取网络超时时间（毫秒）
     */
    uint32_t GetNetworkTimeoutMs() const { return network_timeout_ms_; }

    /**
     * 获取心跳间隔（秒）
     */
    uint32_t GetPingIntervalSec() const { return ping_interval_sec_; }

    /**
     * 获取Pong超时时间（秒）
     */
    uint32_t GetPongTimeoutSec() const { return pong_timeout_sec_; }

    /**
     * 获取心跳定时器间隔（毫秒）
     */
    uint32_t GetHeartbeatIntervalMs() const { return heartbeat_interval_ms_; }

    /**
     * 从NVS加载配置
     */
    void LoadFromNVS();

    /**
     * 保存配置到NVS
     */
    void SaveToNVS();

    /**
     * 自动发现服务器地址
     * @return 是否发现成功
     */
    bool AutoDiscoverServer();

private:
    WebSocketConfig();
    ~WebSocketConfig() = default;

    // 禁用拷贝构造和赋值
    WebSocketConfig(const WebSocketConfig&) = delete;
    WebSocketConfig& operator=(const WebSocketConfig&) = delete;

    std::string server_url_;
    uint32_t reconnect_timeout_ms_;
    uint32_t network_timeout_ms_;
    uint32_t ping_interval_sec_;
    uint32_t pong_timeout_sec_;
    uint32_t heartbeat_interval_ms_;

    static const char* NVS_NAMESPACE;
    static const char* NVS_KEY_SERVER_URL;
};

} // namespace websocket_config

#endif // WEBSOCKET_CONFIG_H 