#include "websocket_config.h"
#include "settings.h"
#include <esp_log.h>
#include <esp_http_client.h>
#include <esp_netif.h>
#include <cJSON.h>
#include <vector>

static const char* TAG = "WebSocketConfig";

namespace websocket_config {

const char* WebSocketConfig::NVS_NAMESPACE = "ws_config";
const char* WebSocketConfig::NVS_KEY_SERVER_URL = "server_url";

WebSocketConfig& WebSocketConfig::GetInstance() {
    static WebSocketConfig instance;
    return instance;
}

WebSocketConfig::WebSocketConfig() :
    server_url_("ws://47.115.37.43:15000/"),
    reconnect_timeout_ms_(5000),
    network_timeout_ms_(10000),
    ping_interval_sec_(15),
    pong_timeout_sec_(5),
    heartbeat_interval_ms_(30000) {
    
    // 尝试从NVS加载配置
    LoadFromNVS();
}

std::string WebSocketConfig::GetServerUrl() const {
    return server_url_;
}

void WebSocketConfig::SetServerUrl(const std::string& url) {
    server_url_ = url;
    ESP_LOGI(TAG, "Server URL updated to: %s", url.c_str());
    
    // 自动保存到NVS
    SaveToNVS();
}

void WebSocketConfig::LoadFromNVS() {
    Settings settings(NVS_NAMESPACE, true);
    
    std::string saved_url = settings.GetString(NVS_KEY_SERVER_URL, "");
    if (!saved_url.empty()) {
        server_url_ = saved_url;
        ESP_LOGI(TAG, "Loaded server URL from NVS: %s", server_url_.c_str());
    } else {
        ESP_LOGI(TAG, "No saved server URL found, using default: %s", server_url_.c_str());
    }
}

void WebSocketConfig::SaveToNVS() {
    Settings settings(NVS_NAMESPACE, true);
    settings.SetString(NVS_KEY_SERVER_URL, server_url_);
    ESP_LOGI(TAG, "Server URL saved to NVS: %s", server_url_.c_str());
}

bool WebSocketConfig::AutoDiscoverServer() {
    ESP_LOGI(TAG, "Starting auto-discovery of WebSocket server");
    
    // 获取当前网络信息
    esp_netif_t* netif = esp_netif_get_handle_from_ifkey("WIFI_STA_DEF");
    if (!netif) {
        ESP_LOGE(TAG, "WiFi interface not found");
        return false;
    }
    
    esp_netif_ip_info_t ip_info;
    if (esp_netif_get_ip_info(netif, &ip_info) != ESP_OK || ip_info.ip.addr == 0) {
        ESP_LOGE(TAG, "WiFi not connected");
        return false;
    }
    
    // 尝试常见的服务器发现方式
    std::vector<std::string> discovery_urls = {
        "http://" + std::to_string((ip_info.gw.addr >> 0) & 0xFF) + "." +
                   std::to_string((ip_info.gw.addr >> 8) & 0xFF) + "." +
                   std::to_string((ip_info.gw.addr >> 16) & 0xFF) + "." +
                   std::to_string((ip_info.gw.addr >> 24) & 0xFF) + ":5000/api/discovery",
        "http://***********:5000/api/discovery",
        "http://***********:5000/api/discovery",
        "http://********:5000/api/discovery"
    };
    
    for (const auto& discovery_url : discovery_urls) {
        ESP_LOGI(TAG, "Trying discovery URL: %s", discovery_url.c_str());
        
        // 配置HTTP客户端
        esp_http_client_config_t config = {};
        config.url = discovery_url.c_str();
        config.method = HTTP_METHOD_GET;
        config.timeout_ms = 3000;
        
        esp_http_client_handle_t client = esp_http_client_init(&config);
        if (!client) {
            continue;
        }
        
        // 执行HTTP请求
        esp_err_t err = esp_http_client_perform(client);
        if (err == ESP_OK) {
            int status_code = esp_http_client_get_status_code(client);
            int content_length = esp_http_client_get_content_length(client);
            
            if (status_code == 200 && content_length > 0) {
                char* response_buffer = (char*)malloc(content_length + 1);
                if (response_buffer) {
                    int read_len = esp_http_client_read(client, response_buffer, content_length);
                    if (read_len > 0) {
                        response_buffer[read_len] = '\0';
                        
                        // 解析发现响应
                        cJSON* json = cJSON_Parse(response_buffer);
                        if (json) {
                            cJSON* ws_url_item = cJSON_GetObjectItem(json, "websocket_url");
                            if (cJSON_IsString(ws_url_item)) {
                                std::string discovered_url = cJSON_GetStringValue(ws_url_item);
                                ESP_LOGI(TAG, "Auto-discovered server: %s", discovered_url.c_str());
                                
                                SetServerUrl(discovered_url);
                                cJSON_Delete(json);
                                free(response_buffer);
                                esp_http_client_cleanup(client);
                                return true;
                            }
                            cJSON_Delete(json);
                        }
                    }
                    free(response_buffer);
                }
            }
        }
        
        esp_http_client_cleanup(client);
    }
    
    ESP_LOGW(TAG, "Auto-discovery failed, using configured server");
    return false;
}

} // namespace websocket_config 