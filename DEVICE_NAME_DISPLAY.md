# ESP32 Compact WiFi Board - 设备名称显示功能

## 功能概述

为ESP32 Compact WiFi Board (128x64 OLED屏幕) 添加了设备名称显示功能，设备名称会显示在OLED屏幕的状态栏中，位于时间的右边。

## 实现的功能

### 1. 设备名称获取
- **本地存储**: 使用NVS存储设备名称，命名空间为"device"，键为"name"
- **Web服务器同步**: 启动时自动从Web服务器获取设备名称
- **IP地址映射**: 根据设备IP地址自动匹配对应的设备名称

### 2. OLED屏幕显示
- **状态栏集成**: 在128x64和128x32两种屏幕尺寸的状态栏中都添加了设备名称标签
- **实时更新**: 设备名称更新时会立即反映到屏幕显示
- **布局优化**: 设备名称显示在时间右侧，不影响其他状态信息

### 3. 自动同步机制
- **网络连接检测**: 等待WiFi连接成功后再尝试获取设备名称
- **异步更新**: 使用Application::Schedule进行异步更新，不阻塞主线程
- **持久化存储**: 从Web服务器获取的设备名称会保存到本地，下次启动时直接使用

## 修改的文件

### 1. `main/boards/bread-compact-wifi/compact_wifi_board.cc`
- 添加了`device_name_`成员变量
- 实现了`LoadDeviceName()`方法
- 实现了`FetchDeviceNameFromWeb()`方法
- 实现了`GetDeviceNameFromWeb()`方法
- 重写了`GetDeviceName()`虚方法

### 2. `main/boards/common/board.h`
- 添加了`GetDeviceName()`虚方法，默认返回"设备"

### 3. `main/display/display.h`
- 添加了`SetDeviceName()`方法声明
- 添加了`device_name_label_`成员变量

### 4. `main/display/display.cc`
- 实现了`SetDeviceName()`方法
- 在析构函数中添加了设备名称标签的清理

### 5. `main/display/oled_display.cc`
- 在`SetupUI_128x64()`中添加了设备名称标签创建
- 在`SetupUI_128x32()`中添加了设备名称标签创建

### 6. `main/application.cc`
- 修改了时间显示逻辑，同时设置设备名称

## 设备名称映射

当前实现的IP地址到设备名称的映射：

```cpp
if (current_ip == "*************") {
    return "刘总";
} else if (current_ip == "*************") {
    return "李总";
} else if (current_ip == "************") {
    return "默认设备";
}
```

## 使用方法

### 1. 编译和烧录
```bash
idf.py build flash monitor
```

### 2. 查看效果
- 设备启动后，OLED屏幕状态栏会显示时间和设备名称
- 格式：`HH:MM 设备名称`
- 例如：`14:30 刘总`

### 3. 自定义设备名称
可以通过以下方式自定义设备名称：

#### 方法1: 修改IP映射
在`GetDeviceNameFromWeb()`方法中添加新的IP地址映射：
```cpp
} else if (current_ip == "192.168.18.XX") {
    return "新设备名称";
```

#### 方法2: 手动设置
通过NVS直接设置：
```cpp
Settings settings("device", true);
settings.SetString("name", "自定义名称");
```

## 技术特点

### 1. 异步处理
- 设备名称获取不会阻塞主线程
- 使用Application::Schedule进行异步调度

### 2. 容错机制
- 网络连接失败时使用默认名称
- 超时保护（最多等待30秒）

### 3. 内存管理
- 正确的LVGL对象生命周期管理
- 在析构函数中清理所有创建的标签

### 4. 扩展性
- 基于虚方法的设计，便于其他Board类型扩展
- 支持不同屏幕尺寸的适配

## 未来改进

1. **HTTP客户端集成**: 实现真正的HTTP请求来从Web服务器获取设备配置
2. **动态更新**: 支持运行时动态更新设备名称
3. **多语言支持**: 支持不同语言的设备名称显示
4. **字体优化**: 针对中文字符优化字体显示效果

## 调试信息

启动时会输出以下日志信息：
```
I (xxx) CompactWifiBoard: Device name: 刘总
I (xxx) CompactWifiBoard: Current device IP: *************
I (xxx) CompactWifiBoard: Device name updated from web: 刘总
```

这些日志可以帮助调试设备名称获取和显示的过程。
