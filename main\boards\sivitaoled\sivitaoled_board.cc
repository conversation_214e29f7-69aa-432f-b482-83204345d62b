#include "wifi_board.h"
#include "audio_codecs/es8311_audio_codec.h"
#include "display/oled_display.h"
#include "application.h"
#include "button.h"
#include "config.h"
#include "led/single_led.h"
#include "assets/lang_config.h"
#include "mcp_wake.h"
#include "mcp_server.h"
#include "iot/thing_manager.h"
#include "settings.h"

#include <esp_log.h>
#include <driver/i2c_master.h>
#include <esp_lcd_panel_io.h>
#include <esp_lcd_panel_ops.h>
#include <esp_lcd_panel_vendor.h>
#include <wifi_station.h>
#include <esp_netif.h>
#include <string>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>

#define TAG "SivitaOledBoard"

// 启用字体
#define FONT_PUHUI_14_1 1
#define FONT_AWESOME_14_1 1

LV_FONT_DECLARE(font_puhui_14_1);
LV_FONT_DECLARE(font_awesome_14_1);

class SivitaOledBoard : public WifiBoard {
private:
    i2c_master_bus_handle_t codec_i2c_bus_;
    esp_lcd_panel_io_handle_t panel_io_ = nullptr;
    esp_lcd_panel_handle_t panel_ = nullptr;
    Display* display_ = nullptr;
    Button boot_button_;
    std::string device_name_;

    void InitializeCodecI2c() {
        ESP_LOGI(TAG, "Initializing I2C bus for audio codec and display");
        i2c_master_bus_config_t i2c_bus_cfg = {
            .i2c_port = I2C_NUM_0,
            .sda_io_num = AUDIO_CODEC_I2C_SDA_PIN,
            .scl_io_num = AUDIO_CODEC_I2C_SCL_PIN,
            .clk_source = I2C_CLK_SRC_DEFAULT,
            .glitch_ignore_cnt = 7,
            .intr_priority = 0,
            .trans_queue_depth = 0,
            .flags = {
                .enable_internal_pullup = 1,
            },
        };
        
        esp_err_t ret = i2c_new_master_bus(&i2c_bus_cfg, &codec_i2c_bus_);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to initialize I2C bus: %s", esp_err_to_name(ret));
        } else {
            ESP_LOGI(TAG, "I2C bus initialized successfully");
        }
    }

    void InitializeSsd1306Display() {
        ESP_LOGI(TAG, "Initializing SSD1306 OLED display");

        // SSD1306 config
        esp_lcd_panel_io_i2c_config_t io_config = {
            .dev_addr = 0x3C,
            .on_color_trans_done = nullptr,
            .user_ctx = nullptr,
            .control_phase_bytes = 1,
            .dc_bit_offset = 6,
            .lcd_cmd_bits = 8,
            .lcd_param_bits = 8,
            .flags = {
                .dc_low_on_data = 0,
                .disable_control_phase = 0,
            },
            .scl_speed_hz = 400 * 1000,
        };

        ESP_ERROR_CHECK(esp_lcd_new_panel_io_i2c_v2(codec_i2c_bus_, &io_config, &panel_io_));

        ESP_LOGI(TAG, "Install SSD1306 driver");
        esp_lcd_panel_dev_config_t panel_config = {};
        panel_config.reset_gpio_num = -1;
        panel_config.bits_per_pixel = 1;

        esp_lcd_panel_ssd1306_config_t ssd1306_config = {
            .height = static_cast<uint8_t>(DISPLAY_HEIGHT),
        };
        panel_config.vendor_config = &ssd1306_config;

        ESP_ERROR_CHECK(esp_lcd_new_panel_ssd1306(panel_io_, &panel_config, &panel_));
        ESP_LOGI(TAG, "SSD1306 driver installed");

        // Reset the display
        ESP_ERROR_CHECK(esp_lcd_panel_reset(panel_));
        if (esp_lcd_panel_init(panel_) != ESP_OK) {
            ESP_LOGE(TAG, "Failed to initialize display");
            display_ = new NoDisplay();
            return;
        }

        // Set the display to on
        ESP_LOGI(TAG, "Turning display on");
        ESP_ERROR_CHECK(esp_lcd_panel_disp_on_off(panel_, true));

        display_ = new OledDisplay(panel_io_, panel_, DISPLAY_WIDTH, DISPLAY_HEIGHT, DISPLAY_MIRROR_X, DISPLAY_MIRROR_Y,
            {&font_puhui_14_1, &font_awesome_14_1});
    }

    void InitializeButtons() {
        ESP_LOGI(TAG, "Initializing buttons");
        
        boot_button_.OnClick([this]() {
            auto& app = Application::GetInstance();
            if (app.GetDeviceState() == kDeviceStateStarting && !WifiStation::GetInstance().IsConnected()) {
                ESP_LOGI(TAG, "Boot button pressed during startup, resetting WiFi config");
                ResetWifiConfiguration();
            }
            ESP_LOGI(TAG, "Toggling chat state");
            app.ToggleChatState();
        });
        
        boot_button_.OnPressDown([this]() {
            Application::GetInstance().StartListening();
        });
        
        boot_button_.OnPressUp([this]() {
            Application::GetInstance().StopListening();
        });
    }

    void InitializePA() {
        ESP_LOGI(TAG, "Initializing PA");
        
        gpio_config_t io_conf = {
            .pin_bit_mask = (1ULL << AUDIO_CODEC_PA_PIN),
            .mode = GPIO_MODE_OUTPUT,
            .pull_up_en = GPIO_PULLUP_DISABLE,
            .pull_down_en = GPIO_PULLDOWN_DISABLE,
            .intr_type = GPIO_INTR_DISABLE,
        };
        ESP_ERROR_CHECK(gpio_config(&io_conf));
        ESP_ERROR_CHECK(gpio_set_level(AUDIO_CODEC_PA_PIN, 1)); // 启用功放
    }

    void LoadDeviceName() {
        // 从设置中读取设备名称
        Settings settings("device", true);
        device_name_ = settings.GetString("name", "设备");

        ESP_LOGI(TAG, "Loaded device name from settings: %s", device_name_.c_str());

        // 总是尝试从网络获取最新的设备名称（异步）
        Application::GetInstance().Schedule([this]() {
            FetchDeviceNameFromWeb();
        });

        // 如果没有设置过设备名称，使用临时默认名称
        if (device_name_ == "设备") {
            device_name_ = "小灵OLED";
        }

        ESP_LOGI(TAG, "Current device name: %s", device_name_.c_str());
    }

    void FetchDeviceNameFromWeb() {
        // 等待WiFi连接
        int retry_count = 0;
        const int max_retries = 30; // 最多等待30秒

        while (retry_count < max_retries) {
            esp_netif_t* netif = esp_netif_get_handle_from_ifkey("WIFI_STA_DEF");
            if (netif) {
                esp_netif_ip_info_t ip_info;
                if (esp_netif_get_ip_info(netif, &ip_info) == ESP_OK && ip_info.ip.addr != 0) {
                    // WiFi已连接，尝试从Web服务器获取设备名称
                    std::string current_ip = std::to_string((ip_info.ip.addr >> 0) & 0xFF) + "." +
                                           std::to_string((ip_info.ip.addr >> 8) & 0xFF) + "." +
                                           std::to_string((ip_info.ip.addr >> 16) & 0xFF) + "." +
                                           std::to_string((ip_info.ip.addr >> 24) & 0xFF);

                    ESP_LOGI(TAG, "Current device IP: %s", current_ip.c_str());

                    // 尝试从Web服务器获取设备名称
                    std::string web_server_url = "http://************:5000"; // 默认Web服务器地址
                    std::string device_name = GetDeviceNameFromWeb(web_server_url, current_ip);

                    ESP_LOGI(TAG, "Resolved device name: %s for IP: %s", device_name.c_str(), current_ip.c_str());

                    if (!device_name.empty() && device_name != "设备") {
                        device_name_ = device_name;
                        // 保存到设置中
                        Settings settings("device", true);
                        settings.SetString("name", device_name_);
                        // 更新显示
                        if (display_) {
                            display_->SetDeviceName(device_name_.c_str());
                        }
                        ESP_LOGI(TAG, "Device name updated from web: %s", device_name_.c_str());
                    }
                    return;
                }
            }
            vTaskDelay(pdMS_TO_TICKS(1000)); // 等待1秒
            retry_count++;
        }

        ESP_LOGW(TAG, "WiFi connection timeout, using default device name");
    }

    std::string GetDeviceNameFromWeb(const std::string& web_server_url, const std::string& current_ip) {
        ESP_LOGI(TAG, "Fetching device name from web server: %s for IP: %s", web_server_url.c_str(), current_ip.c_str());

        // 根据IP地址返回预设的设备名称
        // 这些映射应该与Web服务器的devices.json保持一致
        if (current_ip == "*************") {
            ESP_LOGI(TAG, "Matched IP %s to device: 主显示设备", current_ip.c_str());
            return "主显示设备";
        } else if (current_ip == "*************") {
            ESP_LOGI(TAG, "Matched IP %s to device: LCD设备", current_ip.c_str());
            return "LCD设备";
        } else if (current_ip == "************") {
            ESP_LOGI(TAG, "Matched IP %s to device: OLED显示器", current_ip.c_str());
            return "OLED显示器";
        } else if (current_ip.find("192.168.18.") == 0) {
            // 如果是同一网段的其他IP，使用IP的最后一段作为设备标识
            std::string last_octet = current_ip.substr(current_ip.find_last_of('.') + 1);
            std::string device_name = "小灵" + last_octet + "号";
            ESP_LOGI(TAG, "Generated device name: %s for IP: %s", device_name.c_str(), current_ip.c_str());
            return device_name;
        }

        // 如果没有匹配的IP，返回IP作为设备名称
        ESP_LOGI(TAG, "No match found, using IP as device name: %s", current_ip.c_str());
        return current_ip;
    }

    // 物联网初始化，使用 MCP 协议
    void InitializeIot() {
#if CONFIG_IOT_PROTOCOL_XIAOZHI
        auto& thing_manager = iot::ThingManager::GetInstance();
        thing_manager.AddThing(iot::CreateThing("Speaker"));
        thing_manager.AddThing(iot::CreateThing("Screen"));
#elif CONFIG_IOT_PROTOCOL_MCP
        // 延迟启动远程唤醒HTTP服务器，等待网络栈完全初始化
        Application::GetInstance().Schedule([this]() {
            // 等待WiFi连接
            int retry_count = 0;
            const int max_retries = 30; // 最多等待30秒

            while (retry_count < max_retries) {
                esp_netif_t* netif = esp_netif_get_handle_from_ifkey("WIFI_STA_DEF");
                if (netif) {
                    esp_netif_ip_info_t ip_info;
                    if (esp_netif_get_ip_info(netif, &ip_info) == ESP_OK && ip_info.ip.addr != 0) {
                        // WiFi已连接，启动WebSocket客户端
                        static remote_wake::RemoteWakeClient wake_client;
                        // 设备名称（从board获取）
                        wake_client.RegisterDevice("", device_name_);
                        wake_client.Start();

                        return;
                    }
                }
                vTaskDelay(pdMS_TO_TICKS(1000)); // 等待1秒
                retry_count++;
            }

            ESP_LOGW("RemoteWake", "WiFi connection timeout, HTTP servers not started");
        });
#endif
    }

public:
    SivitaOledBoard() : boot_button_(BOOT_BUTTON_GPIO) {
        InitializeCodecI2c();
        InitializeSsd1306Display();
        InitializeButtons();
        InitializePA();
        LoadDeviceName();
        InitializeIot();

        // 初始设置设备名称到显示器
        if (display_) {
            display_->SetDeviceName(device_name_.c_str());
        }
    }

    virtual Led* GetLed() override {
        static SingleLed led(BUILTIN_LED_GPIO);
        return &led;
    }

    virtual AudioCodec* GetAudioCodec() override {
        static Es8311AudioCodec audio_codec(codec_i2c_bus_, I2C_NUM_0, AUDIO_INPUT_SAMPLE_RATE, AUDIO_OUTPUT_SAMPLE_RATE,
            AUDIO_I2S_GPIO_MCLK, AUDIO_I2S_GPIO_BCLK, AUDIO_I2S_GPIO_WS, AUDIO_I2S_GPIO_DOUT, AUDIO_I2S_GPIO_DIN,
            AUDIO_CODEC_PA_PIN, AUDIO_CODEC_ES8311_ADDR);
        return &audio_codec;
    }

    virtual Display* GetDisplay() override {
        return display_;
    }

    virtual std::string GetDeviceName() const override {
        return device_name_;
    }
};

DECLARE_BOARD(SivitaOledBoard); 