#include "wifi_board.h"
#include "audio_codecs/es8311_audio_codec.h"
#include "display/lcd_display.h"
#include "application.h"
#include "button.h"
#include "config.h"
#include "led/single_led.h"
#include "assets/lang_config.h"
#include "backlight.h"
#include "mcp_server.h"
#include "boards/common/lamp_controller.h"
#include "iot/thing_manager.h"
#include "mcp_wake.h"
#include "settings.h"

#include <esp_log.h>
#include <driver/i2c_master.h>
#include <esp_lcd_panel_io.h>
#include <esp_lcd_panel_ops.h>
#include <esp_lcd_panel_vendor.h>
#include <esp_lcd_panel_st7789.h>
#include <wifi_station.h>
#include <esp_netif.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>

#define TAG "SilingzaowuBoard"

// 启用字体
#define FONT_PUHUI_20_4 1
#define FONT_AWESOME_20_4 1

LV_FONT_DECLARE(font_puhui_20_4);
LV_FONT_DECLARE(font_awesome_20_4);

class SilingzaowuBoard : public WifiBoard {
private:
    i2c_master_bus_handle_t codec_i2c_bus_;
    esp_lcd_panel_io_handle_t panel_io_ = nullptr;
    esp_lcd_panel_handle_t panel_ = nullptr;
    Display* display_ = nullptr;
    Button boot_button_;
    std::string device_name_;

    void InitializeCodecI2c() {
        ESP_LOGI(TAG, "Initializing I2C bus for audio codec");
        i2c_master_bus_config_t i2c_bus_cfg = {
            .i2c_port = I2C_NUM_0,
            .sda_io_num = AUDIO_CODEC_I2C_SDA_PIN,
            .scl_io_num = AUDIO_CODEC_I2C_SCL_PIN,
            .clk_source = I2C_CLK_SRC_DEFAULT,
            .glitch_ignore_cnt = 7,
            .intr_priority = 0,
            .trans_queue_depth = 0,
            .flags = {
                .enable_internal_pullup = 1,
            },
        };
        
        esp_err_t ret = i2c_new_master_bus(&i2c_bus_cfg, &codec_i2c_bus_);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to initialize I2C bus: %s", esp_err_to_name(ret));
        } else {
            ESP_LOGI(TAG, "I2C bus initialized successfully");
        }
    }

    void InitializeDisplay() {
        ESP_LOGI(TAG, "Initializing display");
        
        // 配置SPI总线
        spi_bus_config_t buscfg = {
            .mosi_io_num = DISPLAY_SPI_MOSI_PIN,
            .miso_io_num = -1,  // 不使用MISO
            .sclk_io_num = DISPLAY_SPI_SCLK_PIN,
            .quadwp_io_num = -1,
            .quadhd_io_num = -1,
            .max_transfer_sz = DISPLAY_WIDTH * DISPLAY_HEIGHT * sizeof(uint16_t),
        };
        
        // 初始化SPI总线
        ESP_ERROR_CHECK(spi_bus_initialize(SPI2_HOST, &buscfg, SPI_DMA_CH_AUTO));
        
        // 配置LCD面板IO
        esp_lcd_panel_io_spi_config_t io_config = {
            .cs_gpio_num = DISPLAY_SPI_CS_PIN,
            .dc_gpio_num = DISPLAY_SPI_DC_PIN,
            .spi_mode = DISPLAY_SPI_MODE,
            .pclk_hz = DISPLAY_SPI_SCLK_HZ,
            .trans_queue_depth = 10,
            .on_color_trans_done = nullptr,
            .user_ctx = nullptr,
            .lcd_cmd_bits = 8,
            .lcd_param_bits = 8,
        };
        
        // 初始化LCD面板IO
        ESP_ERROR_CHECK(esp_lcd_new_panel_io_spi((esp_lcd_spi_bus_handle_t)SPI2_HOST, &io_config, &panel_io_));
        
        // 配置LCD面板
        esp_lcd_panel_dev_config_t panel_config = {
            .reset_gpio_num = DISPLAY_SPI_RESET_PIN,
            .rgb_ele_order = DISPLAY_RGB_ORDER,
            .bits_per_pixel = 16,
            .flags = {
                .reset_active_high = 0,
            },
        };
        
        // 初始化ST7789面板
        ESP_ERROR_CHECK(esp_lcd_new_panel_st7789(panel_io_, &panel_config, &panel_));
        
        // 重置面板
        ESP_ERROR_CHECK(esp_lcd_panel_reset(panel_));
        
        // 初始化面板
        ESP_ERROR_CHECK(esp_lcd_panel_init(panel_));
        
        // 设置面板方向
        ESP_ERROR_CHECK(esp_lcd_panel_mirror(panel_, DISPLAY_MIRROR_X, DISPLAY_MIRROR_Y));
        ESP_ERROR_CHECK(esp_lcd_panel_swap_xy(panel_, DISPLAY_SWAP_XY));
        
        // 反转颜色
        if (DISPLAY_INVERT_COLOR) {
            ESP_ERROR_CHECK(esp_lcd_panel_invert_color(panel_, true));
        }
        
        // 打开显示
        ESP_ERROR_CHECK(esp_lcd_panel_disp_on_off(panel_, true));
        
        // 配置背光
        gpio_config_t io_conf = {
            .pin_bit_mask = (1ULL << DISPLAY_BACKLIGHT_PIN),
            .mode = GPIO_MODE_OUTPUT,
            .pull_up_en = GPIO_PULLUP_DISABLE,
            .pull_down_en = GPIO_PULLDOWN_DISABLE,
            .intr_type = GPIO_INTR_DISABLE,
        };
        ESP_ERROR_CHECK(gpio_config(&io_conf));
        
        // 打开背光
        bool level = !DISPLAY_BACKLIGHT_OUTPUT_INVERT;
        ESP_ERROR_CHECK(gpio_set_level(DISPLAY_BACKLIGHT_PIN, level));
        
        // 创建显示对象 - 使用正确的字体避免崩溃
        DisplayFonts fonts = {
            .text_font = &font_puhui_20_4,  // 使用已声明的文本字体
            .icon_font = &font_awesome_20_4,  // 使用已声明的图标字体
#if CONFIG_USE_WECHAT_MESSAGE_STYLE
            .emoji_font = font_emoji_32_init(),
#else
            .emoji_font = DISPLAY_HEIGHT >= 240 ? font_emoji_64_init() : font_emoji_32_init(),
#endif
        };
        display_ = new SpiLcdDisplay(panel_io_, panel_, DISPLAY_WIDTH, DISPLAY_HEIGHT,
                                   DISPLAY_OFFSET_X, DISPLAY_OFFSET_Y, // offset_x, offset_y
                                   DISPLAY_MIRROR_X, DISPLAY_MIRROR_Y, DISPLAY_SWAP_XY,
                                   fonts);
    }

    void InitializeButtons() {
        ESP_LOGI(TAG, "Initializing buttons");
        
        boot_button_.OnClick([this]() {
            auto& app = Application::GetInstance();
            if (app.GetDeviceState() == kDeviceStateStarting && !WifiStation::GetInstance().IsConnected()) {
                ESP_LOGI(TAG, "Boot button pressed during startup, resetting WiFi config");
                ResetWifiConfiguration();
                return;
            }
            
            // 自定义按键逻辑：聆听中按下变为待命，说话中按下变为聆听，待命时按下变为聆听
            auto current_state = app.GetDeviceState();
            if (current_state == kDeviceStateListening) {
                ESP_LOGI(TAG, "Boot button pressed during listening, switching to idle");
                app.SetDeviceState(kDeviceStateIdle);
                // 清空聊天消息，回到待命状态表情显示
                auto display = Board::GetInstance().GetDisplay();
                if (display) {
                    display->SetChatMessage("system", "");
                    display->SetEmotion("neutral");
                }
            } else if (current_state == kDeviceStateSpeaking) {
                ESP_LOGI(TAG, "Boot button pressed during speaking, switching to listening");
                app.SetDeviceState(kDeviceStateListening);
            } else if (current_state == kDeviceStateIdle) {
                ESP_LOGI(TAG, "Boot button pressed during idle, switching to listening");
                app.SetDeviceState(kDeviceStateListening);
            } else {
                ESP_LOGI(TAG, "Boot button pressed, toggling chat state");
                app.ToggleChatState();
            }
        });
    }

    void InitializePA() {
        ESP_LOGI(TAG, "Initializing PA");
        
        gpio_config_t io_conf = {
            .pin_bit_mask = (1ULL << AUDIO_CODEC_PA_PIN),
            .mode = GPIO_MODE_OUTPUT,
            .pull_up_en = GPIO_PULLUP_DISABLE,
            .pull_down_en = GPIO_PULLDOWN_DISABLE,
            .intr_type = GPIO_INTR_DISABLE,
        };
        ESP_ERROR_CHECK(gpio_config(&io_conf));
        ESP_ERROR_CHECK(gpio_set_level(AUDIO_CODEC_PA_PIN, 1)); // 启用功放
    }

    // 物联网初始化，逐步迁移到 MCP 协议
    void InitializeIot() {
#if CONFIG_IOT_PROTOCOL_XIAOZHI
        auto& thing_manager = iot::ThingManager::GetInstance();
        thing_manager.AddThing(iot::CreateThing("Speaker"));
        thing_manager.AddThing(iot::CreateThing("Lamp"));
#elif CONFIG_IOT_PROTOCOL_MCP
        static LampController lamp(LAMP_GPIO);

        // 延迟启动远程唤醒WebSocket客户端，等待网络栈完全初始化
        Application::GetInstance().Schedule([this]() {
            // 等待WiFi连接
            int retry_count = 0;
            const int max_retries = 30; // 最多等待30秒

            while (retry_count < max_retries) {
                esp_netif_t* netif = esp_netif_get_handle_from_ifkey("WIFI_STA_DEF");
                if (netif) {
                    esp_netif_ip_info_t ip_info;
                    if (esp_netif_get_ip_info(netif, &ip_info) == ESP_OK && ip_info.ip.addr != 0) {
                        // WiFi已连接，启动WebSocket客户端
                        static remote_wake::RemoteWakeClient wake_client;
                        // 设置设备信息（不会产生警告）
                        wake_client.SetDeviceInfo("", device_name_);
                        // 启动WebSocket连接，连接成功后会自动注册设备
                        wake_client.Start();

                        return;
                    }
                }
                vTaskDelay(pdMS_TO_TICKS(1000)); // 等待1秒
                retry_count++;
            }

            ESP_LOGW("RemoteWake", "WiFi connection timeout, WebSocket client not started");
        });
#endif
    }

public:
    SilingzaowuBoard() : boot_button_(BOOT_BUTTON_GPIO) {
        // 设置设备名称
        Settings settings("board", true);
        device_name_ = settings.GetString("device_name", "Sivita-Live-LCD");

        InitializeCodecI2c();
        InitializeDisplay();
        InitializeButtons();
        InitializePA();
        InitializeIot();

        // 初始化后恢复背光亮度
        if (DISPLAY_BACKLIGHT_PIN != GPIO_NUM_NC) {
            GetBacklight()->RestoreBrightness();
        }
    }

    virtual Led* GetLed() override {
        static SingleLed led(BUILTIN_LED_GPIO);
        return &led;
    }

    virtual AudioCodec* GetAudioCodec() override {
        static Es8311AudioCodec audio_codec(codec_i2c_bus_, I2C_NUM_0, AUDIO_INPUT_SAMPLE_RATE, AUDIO_OUTPUT_SAMPLE_RATE,
            AUDIO_I2S_GPIO_MCLK, AUDIO_I2S_GPIO_BCLK, AUDIO_I2S_GPIO_WS, AUDIO_I2S_GPIO_DOUT, AUDIO_I2S_GPIO_DIN,
            AUDIO_CODEC_PA_PIN, AUDIO_CODEC_ES8311_ADDR);
        return &audio_codec;
    }

    virtual Display* GetDisplay() override {
        // 如果display_为空，记录错误并创建一个空的显示对象
        if (display_ == nullptr) {
            ESP_LOGE(TAG, "Display is null, returning empty display");
            // 创建最小的空显示对象防止崩溃
            DisplayFonts empty_fonts = {
                .text_font = nullptr,
                .icon_font = nullptr,
                .emoji_font = nullptr
            };
            display_ = new SpiLcdDisplay(panel_io_, panel_, DISPLAY_WIDTH, DISPLAY_HEIGHT,
                                    DISPLAY_OFFSET_X, DISPLAY_OFFSET_Y, // offset_x, offset_y
                                    DISPLAY_MIRROR_X, DISPLAY_MIRROR_Y, DISPLAY_SWAP_XY,
                                    empty_fonts);
        }
        return display_;
    }
    
    virtual Backlight* GetBacklight() override {
        if (DISPLAY_BACKLIGHT_PIN != GPIO_NUM_NC) {
            static PwmBacklight backlight(DISPLAY_BACKLIGHT_PIN, DISPLAY_BACKLIGHT_OUTPUT_INVERT);
            return &backlight;
        }
        return nullptr;
    }

    virtual std::string GetDeviceName() const override {
        return device_name_;
    }
};

DECLARE_BOARD(SilingzaowuBoard); 