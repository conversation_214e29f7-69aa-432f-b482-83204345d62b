#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试设备重启功能
通过WebSocket发送重启指令到ESP32设备
"""

import asyncio
import websockets
import json
from datetime import datetime

async def test_reboot_device():
    """测试设备重启功能"""
    server_url = "ws://127.0.0.1:15000"  # 本地测试
    # server_url = "ws://106.14.123.12:15000"  # 远程服务器
    
    print("🧪 测试设备重启功能")
    print("=" * 50)
    print(f"🔗 连接到服务器: {server_url}")
    
    try:
        async with websockets.connect(server_url) as websocket:
            print("✅ WebSocket连接建立")
            
            # 测试重启指令
            device_id = "30:ed:a0:23:82:ec"  # 替换为实际的设备ID
            
            reboot_message = {
                "type": "reboot_device",
                "data": {
                    "device_id": device_id
                }
            }
            
            print(f"📤 发送重启指令到设备: {device_id}")
            print(f"📋 消息内容: {json.dumps(reboot_message, ensure_ascii=False, indent=2)}")
            
            await websocket.send(json.dumps(reboot_message, ensure_ascii=False))
            print("✅ 重启指令已发送")
            
            # 等待响应
            print("⏳ 等待服务器响应...")
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                print(f"📨 收到响应: {response}")
                
                # 解析响应
                response_data = json.loads(response)
                if response_data.get("type") == "reboot_response":
                    data = response_data.get("data", {})
                    if data.get("success"):
                        print("✅ 重启指令发送成功！")
                        print(f"📝 响应消息: {data.get('message', '')}")
                        print(f"🎯 目标设备: {data.get('target_device', '')}")
                    else:
                        print("❌ 重启指令发送失败！")
                        print(f"📝 错误消息: {data.get('message', '')}")
                else:
                    print(f"⚠️ 收到意外响应类型: {response_data.get('type')}")
                    
            except asyncio.TimeoutError:
                print("⏰ 等待响应超时")
            except Exception as e:
                print(f"❌ 处理响应时出错: {e}")
                
    except Exception as e:
        print(f"❌ 连接失败: {e}")

async def test_multiple_devices():
    """测试多设备重启"""
    server_url = "ws://127.0.0.1:15000"
    
    # 多个设备ID（根据实际情况修改）
    device_ids = [
        "30:ed:a0:23:82:ec",
        "30:ed:a0:23:82:ed", 
        "30:ed:a0:23:82:ee"
    ]
    
    print("🧪 测试多设备重启功能")
    print("=" * 50)
    
    try:
        async with websockets.connect(server_url) as websocket:
            print("✅ WebSocket连接建立")
            
            for device_id in device_ids:
                print(f"\n📤 发送重启指令到设备: {device_id}")
                
                reboot_message = {
                    "type": "reboot_device", 
                    "data": {
                        "device_id": device_id
                    }
                }
                
                await websocket.send(json.dumps(reboot_message, ensure_ascii=False))
                
                # 等待响应
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response_data = json.loads(response)
                    
                    if response_data.get("type") == "reboot_response":
                        data = response_data.get("data", {})
                        if data.get("success"):
                            print(f"✅ 设备 {device_id} 重启指令发送成功")
                        else:
                            print(f"❌ 设备 {device_id} 重启指令发送失败: {data.get('message', '')}")
                    
                except asyncio.TimeoutError:
                    print(f"⏰ 设备 {device_id} 响应超时")
                
                # 设备间间隔
                await asyncio.sleep(1)
                
    except Exception as e:
        print(f"❌ 连接失败: {e}")

def main():
    """主函数"""
    print("🎯 ESP32设备重启测试工具")
    print("=" * 50)
    print("1. 单设备重启测试")
    print("2. 多设备重启测试")
    print("=" * 50)
    
    choice = input("请选择测试类型 (1/2): ").strip()
    
    if choice == "1":
        asyncio.run(test_reboot_device())
    elif choice == "2":
        asyncio.run(test_multiple_devices())
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
