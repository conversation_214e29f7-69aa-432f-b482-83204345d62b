#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP32设备唤醒测试工具
用于测试WebSocket主动唤醒功能
"""

import asyncio
import websockets
import json
import sys

class ESP32WakeTest:
    def __init__(self, server_url="ws://47.115.37.43:15000/"):
        self.server_url = server_url
        self.websocket = None
    
    async def connect(self):
        """连接到WebSocket服务器"""
        try:
            print(f"🔗 连接到服务器: {self.server_url}")
            self.websocket = await websockets.connect(self.server_url)
            print("✅ 连接成功")
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    async def send_wake_device(self, device_id, message="", force=False):
        """发送唤醒设备指令"""
        if not self.websocket:
            print("❌ 未连接到服务器")
            return
        
        wake_data = {
            "device_id": device_id,
            "message": message,
            "force": force
        }
        
        # 构造标准JSON格式消息
        wake_message = json.dumps({
            "type": "wake_device",
            "data": wake_data
        }, ensure_ascii=False)
        
        try:
            await self.websocket.send(wake_message)
            print(f"📤 发送唤醒指令:")
            print(f"   设备ID: {device_id}")
            print(f"   消息: {message}")
            print(f"   强制模式: {force}")
            print(f"   原始消息: {wake_message}")
        except Exception as e:
            print(f"❌ 发送失败: {e}")
    
    async def send_speak_message(self, device_id, message, force=False):
        """发送说话指令"""
        if not self.websocket:
            print("❌ 未连接到服务器")
            return
        
        speak_data = {
            "device_id": device_id,
            "message": message,
            "force": force
        }
        
        speak_message = json.dumps({
            "type": "speak_message",
            "data": speak_data
        }, ensure_ascii=False)
        
        try:
            await self.websocket.send(speak_message)
            print(f"📤 发送说话指令:")
            print(f"   设备ID: {device_id}")
            print(f"   消息: {message}")
            print(f"   强制模式: {force}")
        except Exception as e:
            print(f"❌ 发送失败: {e}")
    
    async def send_reconnect_command(self, device_id):
        """发送重连服务器指令"""
        if not self.websocket:
            print("❌ 未连接到服务器")
            return
        
        reconnect_data = {
            "device_id": device_id
        }
        
        reconnect_message = json.dumps({
            "type": "reconnect_server",
            "data": reconnect_data
        }, ensure_ascii=False)
        
        try:
            await self.websocket.send(reconnect_message)
            print(f"📤 发送重连指令:")
            print(f"   设备ID: {device_id}")
            print(f"   原始消息: {reconnect_message}")
        except Exception as e:
            print(f"❌ 发送失败: {e}")
    
    async def request_device_status(self):
        """请求查看所有设备状态"""
        if not self.websocket:
            print("❌ 未连接到服务器")
            return
        
        # 发送设备状态请求
        status_request = json.dumps({
            "type": "get_all_devices",
            "data": {}
        }, ensure_ascii=False)
        
        try:
            await self.websocket.send(status_request)
            print("📤 已请求设备状态信息，等待服务器响应...")
            
            # 等待并显示服务器响应
            await asyncio.sleep(2)  # 给服务器时间响应
            
        except Exception as e:
            print(f"❌ 请求失败: {e}")
    
    async def listen_for_responses(self):
        """监听服务器响应"""
        if not self.websocket:
            return
        
        try:
            async for message in self.websocket:
                print(f"📨 收到响应: {message}")
                
                # 解析响应
                if message.startswith('{'):
                    try:
                        response_data = json.loads(message)
                        if 'type' in response_data:
                            event_name = response_data['type']
                            event_data = response_data.get('data', {})
                            
                            if event_name == "device_list_response":
                                # 特殊处理设备列表响应
                                devices = event_data.get("devices", [])
                                total_count = event_data.get("total_count", 0)
                                timestamp = event_data.get("timestamp", "")
                                
                                print("\n" + "="*80)
                                print(f"📊 设备状态列表 ({total_count} 台设备)")
                                print("="*80)
                                
                                if devices:
                                    for device in devices:
                                        device_id = device.get('device_id', '')
                                        device_name = device.get('device_name', 'Unknown')
                                        device_state = device.get('device_state', 'unknown')
                                        can_speak = device.get('can_speak', False)
                                        voice_detected = device.get('voice_detected', False)
                                        last_update = device.get('last_update', 'Never')
                                        
                                        # 状态emoji
                                        state_emoji = {
                                            'idle': '💤', 'listening': '👂', 'speaking': '🗣️',
                                            'connecting': '🔄', 'starting': '⚡', 'activating': '🎯',
                                            'wifi_configuring': '📡', 'unknown': '❓'
                                        }.get(device_state, '❓')
                                        
                                        speak_status = '🎤✅' if can_speak else '🎤❌'
                                        voice_status = '🔊✅' if voice_detected else '🔊❌'
                                        device_short = f"{device_id[:8]}...{device_id[-4:]}" if len(device_id) > 12 else device_id
                                        
                                        print(f"🔹 {device_name}")
                                        print(f"   ID: {device_short}")
                                        print(f"   状态: {state_emoji} {device_state.upper()}")
                                        print(f"   功能: {speak_status} {voice_status}")
                                        print(f"   更新: {last_update}")
                                        print("-" * 80)
                                else:
                                    print("📭 当前没有连接的设备")
                                
                                print(f"⏰ 查询时间: {timestamp}")
                                print("="*80)
                            else:
                                # 其他响应的通用处理
                                print(f"   事件: {event_name}")
                                print(f"   数据: {json.dumps(event_data, ensure_ascii=False, indent=2)}")
                    except Exception as e:
                        print(f"❌ 解析响应失败: {e}")
        except websockets.exceptions.ConnectionClosed:
            print("🔌 连接已关闭")
        except Exception as e:
            print(f"❌ 监听错误: {e}")
    
    async def close(self):
        """关闭连接"""
        if self.websocket:
            await self.websocket.close()
            print("🔌 连接已关闭")

async def interactive_test():
    """交互式测试"""
    print("🎯 ESP32设备唤醒测试工具")
    print("=" * 50)
    
    # 输入服务器地址
    server_url = input("输入WebSocket服务器地址 (默认: ws://*************:15000/): ").strip()
    if not server_url:
        server_url = "ws://*************:15000/"
    
    tester = ESP32WakeTest(server_url)
    
    # 连接服务器
    if not await tester.connect():
        return
    
    # 启动响应监听
    listen_task = asyncio.create_task(tester.listen_for_responses())
    
    try:
        while True:
            print("\n📋 可用命令:")
            print("1. wake - 唤醒设备")
            print("2. speak - 让设备说话")
            print("3. reconnect - 重连服务器")
            print("4. status - 查看设备状态")
            print("5. quit - 退出")
            
            command = input("\n请选择命令 (1/2/3/4/5): ").strip()
            
            if command in ['5', 'quit', 'q']:
                break
            elif command in ['1', 'wake']:
                device_id = input("输入设备ID (如: 30:ed:a0:23:82:ec): ").strip()
                message = input("输入消息内容 (可选): ").strip()
                force = input("是否强制模式? (y/N): ").strip().lower() == 'y'
                
                await tester.send_wake_device(device_id, message, force)
                
            elif command in ['2', 'speak']:
                device_id = input("输入设备ID (如: 30:ed:a0:23:82:ec): ").strip()
                message = input("输入要说的内容: ").strip()
                force = input("是否强制模式? (y/N): ").strip().lower() == 'y'
                
                if message:
                    await tester.send_speak_message(device_id, message, force)
                else:
                    print("❌ 消息内容不能为空")
            elif command in ['3', 'reconnect']:
                device_id = input("输入设备ID (如: 30:ed:a0:23:82:ec): ").strip()
                
                if device_id:
                    await tester.send_reconnect_command(device_id)
                else:
                    print("❌ 设备ID不能为空")
            elif command in ['4', 'status']:
                await tester.request_device_status()
            else:
                print("❌ 无效命令")
            
            # 等待一下看响应
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        print("\n👋 用户中断")
    finally:
        listen_task.cancel()
        await tester.close()

async def quick_test():
    """快速测试"""
    print("🚀 快速唤醒测试")
    
    tester = ESP32WakeTest()
    if await tester.connect():
        # 测试唤醒
        await tester.send_wake_device("30:ed:a0:23:82:ec", "这是一个测试消息")
        
        # 等待响应
        await asyncio.sleep(3)
        
        await tester.close()

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "quick":
        asyncio.run(quick_test())
    else:
        asyncio.run(interactive_test()) 