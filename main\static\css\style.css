/* ESP32 定时任务管理界面样式 */

:root {
    --primary-color: #0d6efd;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 8px;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.15s ease-in-out;
}

body {
    background-color: #f5f6fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    margin-bottom: 1rem;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid #dee2e6;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    padding: 1rem 1.25rem;
}

.card-title {
    color: var(--dark-color);
    font-weight: 600;
}

/* 设备状态卡片 */
.device-status-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.device-status-card .card-header {
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.device-status-card .card-title {
    color: white;
}

.status-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.status-item label {
    font-size: 0.875rem;
    opacity: 0.8;
    margin-bottom: 0.25rem;
}

.status-item span {
    font-weight: 600;
    font-size: 1rem;
}

/* 按钮样式 */
.btn {
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

.btn-group .btn-check:checked + .btn {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* 表单样式 */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.input-group {
    border-radius: var(--border-radius);
}

.input-group-text {
    background-color: var(--light-color);
    border-color: #ced4da;
}

/* 周选择器样式 */
.week-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.week-selector .form-check {
    margin-bottom: 0.5rem;
}

.week-selector .form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 任务项样式 */
.task-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 0.75rem;
    transition: var(--transition);
}

.task-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0.25rem 0.5rem rgba(13, 110, 253, 0.15);
}

.task-time {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.task-message {
    font-size: 1rem;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.task-details {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.75rem;
}

.task-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

.task-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* 状态徽章 */
.badge {
    border-radius: 4px;
    font-weight: 500;
}

.status-idle {
    background-color: var(--success-color);
}

.status-listening {
    background-color: var(--info-color);
}

.status-speaking {
    background-color: var(--warning-color);
}

.status-error {
    background-color: var(--danger-color);
}

.status-unknown {
    background-color: #6c757d;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.navbar-brand i {
    font-size: 1.5rem;
}

/* 动画效果 */
@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.loading {
    animation: pulse 1.5s ease-in-out infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 0.5rem;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    .week-selector {
        justify-content: center;
    }
    
    .week-selector .form-check {
        margin: 0.25rem;
    }
    
    .task-actions {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .status-item {
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .row .col-md-8,
    .row .col-md-4 {
        margin-bottom: 1rem;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        border-radius: var(--border-radius) !important;
        margin-bottom: 0.25rem;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 模态框样式 */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    background-color: var(--light-color);
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    background-color: var(--light-color);
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state p {
    font-size: 1.125rem;
    margin-bottom: 0;
}

/* 加载状态 */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 成功/错误消息样式 */
.alert {
    border-radius: var(--border-radius);
    border: none;
    margin-bottom: 1rem;
}

.alert-success {
    background-color: #d1edff;
    color: #0c63e4;
}

.alert-error {
    background-color: #f8d7da;
    color: #721c24;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: var(--dark-color);
    border-radius: 4px;
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    
    .card {
        background-color: #2d2d2d;
        color: #ffffff;
    }
    
    .form-control {
        background-color: #3d3d3d;
        border-color: #4d4d4d;
        color: #ffffff;
    }
    
    .form-control:focus {
        background-color: #3d3d3d;
        border-color: var(--primary-color);
        color: #ffffff;
    }
} 