#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的WebSocket服务器测试
解决事件循环问题
"""

import asyncio
import websockets
import json
import os
from datetime import datetime

# 全局设备存储
connected_devices = {}
device_configs = {}

async def handle_client(websocket, path):
    """处理客户端连接"""
    client_id = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
    print(f"🔗 新连接: {client_id} -> {path}")
    
    try:
        async for message in websocket:
            print(f"📨 收到消息 [{client_id}]: {message}")
            
            # 检查是否是标准JSON格式消息
            if isinstance(message, str) and message.startswith('{'):
                print(f"🤖 检测到ESP32 JSON消息: {message}")
                try:
                    # 解析标准JSON消息：{"type": "event_name", "data": {...}}
                    message_data = json.loads(message)
                    
                    if 'type' in message_data:
                        event_name = message_data['type']
                        event_data = message_data.get('data', {})
                        
                        print(f"🔧 解析ESP32消息: 事件={event_name}, 数据={event_data}")
                        
                        if event_name == 'device_register':
                            # 处理设备注册
                            device_id = event_data.get('device_id')
                            device_name = event_data.get('device_name', device_id)
                            device_type = event_data.get('device_type', 'esp32')
                            capabilities = event_data.get('capabilities', '')

                            print("=" * 50)
                            print("🔧 ESP32设备注册处理器被调用了！")
                            print(f"🔍 客户端ID: {client_id}")
                            print(f"🔍 设备ID: {device_id}")
                            print(f"🔍 设备名称: {device_name}")
                            print(f"🔍 设备类型: {device_type}")
                            print(f"🔍 设备功能: {capabilities}")
                            print("=" * 50)

                            # 直接在本地注册设备，不使用shared_data
                            device_info = {
                                'device_name': device_name,
                                'device_type': device_type,
                                'capabilities': capabilities,
                                'client_id': client_id,
                                'ip': websocket.remote_address[0]
                            }

                            # 保存到本地连接列表
                            connected_devices[device_id] = {
                                'websocket': websocket,
                                'device_info': device_info,
                                'connect_time': datetime.now().isoformat(),
                                'device_state': 'idle'
                            }

                            # 保存到配置文件
                            device_configs[device_id] = {
                                'device_id': device_id,
                                'device_name': device_name,
                                'device_type': device_type,
                                'capabilities': capabilities,
                                'created_at': datetime.now().isoformat(),
                                'last_seen': datetime.now().isoformat(),
                                'enabled': True,
                                'auto_registered': True
                            }

                            # 保存配置文件
                            save_device_configs()

                            # 保存运行时数据
                            save_runtime_data()

                            # 发送注册成功响应
                            response = json.dumps({
                                "type": "device_register_response",
                                "data": {"success": True, "message": "Device registered successfully"}
                            }, ensure_ascii=False)
                            await websocket.send(response)
                            print(f"📤 发送响应: {response}")
                            print(f"✅ 设备注册成功: {device_id}")
                            print(f"🔍 当前连接设备: {list(connected_devices.keys())}")
                            print("=" * 50)
                        
                        elif event_name == 'device_status':
                            device_id = event_data.get('device_id')
                            device_state = event_data.get('device_state', 'unknown')
                            timestamp = event_data.get('timestamp', 0)
                            can_speak = event_data.get('can_speak', False)
                            voice_detected = event_data.get('voice_detected', False)
                            
                            # 获取状态显示emoji
                            state_emoji = {
                                'idle': '💤',
                                'listening': '👂',
                                'speaking': '🗣️',
                                'connecting': '🔄',
                                'starting': '⚡',
                                'activating': '🎯',
                                'wifi_configuring': '📡',
                                'unknown': '❓'
                            }.get(device_state, '❓')
                            
                            print("=" * 60)
                            print(f"📊 {state_emoji} 设备状态实时更新")
                            print(f"🔍 设备ID: {device_id}")
                            print(f"🎯 状态变化: -> {device_state.upper()}")
                            print(f"🎤 可以说话: {'✅' if can_speak else '❌'}")
                            print(f"🔊 检测到语音: {'✅' if voice_detected else '❌'}")
                            print(f"⏰ 时间戳: {timestamp}")
                            
                            # 更新设备状态
                            if device_id in connected_devices:
                                old_state = connected_devices[device_id].get('device_state', 'unknown')
                                connected_devices[device_id]['device_state'] = device_state
                                connected_devices[device_id]['can_speak'] = can_speak
                                connected_devices[device_id]['voice_detected'] = voice_detected
                                connected_devices[device_id]['last_update'] = datetime.now().isoformat()
                                
                                if old_state != device_state:
                                    print(f"📈 状态转换: {old_state} -> {device_state}")
                                
                                # 保存运行时数据
                                save_runtime_data()
                                
                                print(f"✅ 设备状态已更新并保存")
                            else:
                                print(f"⚠️ 设备未注册，无法更新状态")
                            print("=" * 60)
                        
                        elif event_name == 'wake_device':
                            # 处理唤醒设备指令
                            target_device_id = event_data.get('device_id', '')
                            message = event_data.get('message', '')
                            force = event_data.get('force', False)
                            
                            print("=" * 50)
                            print("🎯 收到设备唤醒指令！")
                            print(f"🔍 目标设备: {target_device_id}")
                            print(f"🔍 消息内容: {message}")
                            print(f"🔍 强制模式: {force}")
                            print("=" * 50)
                            
                            if target_device_id and target_device_id in connected_devices:
                                # 找到目标设备，转发消息
                                target_websocket = connected_devices[target_device_id]['websocket']
                                
                                # 构造转发消息（移除device_id字段，因为设备知道自己的ID）
                                forward_message = json.dumps({
                                    "type": "wake_device",
                                    "data": {
                                        'message': message,
                                        'force': force
                                    }
                                }, ensure_ascii=False)
                                
                                try:
                                    await target_websocket.send(forward_message)
                                    print(f"✅ 消息已转发到设备: {target_device_id}")
                                    print(f"📤 转发内容: {forward_message}")
                                    
                                    # 向发送方回复成功
                                    response = json.dumps({
                                        "type": "wake_response",
                                        "data": {"success": True, "message": "Wake command sent to device", "target_device": target_device_id}
                                    }, ensure_ascii=False)
                                    await websocket.send(response)
                                    print(f"📤 发送成功响应: {response}")
                                    
                                except Exception as e:
                                    print(f"❌ 转发消息失败: {e}")
                                    # 向发送方回复失败
                                    response = json.dumps({
                                        "type": "wake_response",
                                        "data": {"success": False, "message": f"Failed to send wake command: {str(e)}"}
                                    }, ensure_ascii=False)
                                    await websocket.send(response)
                            else:
                                print(f"❌ 目标设备未找到或未连接: {target_device_id}")
                                print(f"🔍 当前连接设备: {list(connected_devices.keys())}")
                                
                                # 向发送方回复设备未找到
                                response = json.dumps({
                                    "type": "wake_response",
                                    "data": {"success": False, "message": f"Device not found or not connected: {target_device_id}", "connected_devices": list(connected_devices.keys())}
                                }, ensure_ascii=False)
                                await websocket.send(response)
                        
                        elif event_name == 'speak_message':
                            # 处理说话指令
                            target_device_id = event_data.get('device_id', '')
                            message = event_data.get('message', '')
                            force = event_data.get('force', False)
                            
                            print("=" * 50)
                            print("🎤 收到设备说话指令！")
                            print(f"🔍 目标设备: {target_device_id}")
                            print(f"🔍 消息内容: {message}")
                            print(f"🔍 强制模式: {force}")
                            print("=" * 50)
                            
                            if target_device_id and target_device_id in connected_devices:
                                # 找到目标设备，转发消息
                                target_websocket = connected_devices[target_device_id]['websocket']
                                
                                # 构造转发消息
                                forward_message = json.dumps({
                                    "type": "speak_message",
                                    "data": {
                                        'message': message,
                                        'force': force
                                    }
                                }, ensure_ascii=False)
                                
                                try:
                                    await target_websocket.send(forward_message)
                                    print(f"✅ 说话消息已转发到设备: {target_device_id}")
                                    print(f"📤 转发内容: {forward_message}")
                                    
                                    # 向发送方回复成功
                                    response = json.dumps({
                                        "type": "speak_response",
                                        "data": {"success": True, "message": "Speak command sent to device", "target_device": target_device_id}
                                    }, ensure_ascii=False)
                                    await websocket.send(response)
                                    print(f"📤 发送成功响应: {response}")
                                    
                                except Exception as e:
                                    print(f"❌ 转发说话消息失败: {e}")
                                    # 向发送方回复失败
                                    response = json.dumps({
                                        "type": "speak_response",
                                        "data": {"success": False, "message": f"Failed to send speak command: {str(e)}"}
                                    }, ensure_ascii=False)
                                    await websocket.send(response)
                            else:
                                print(f"❌ 目标设备未找到或未连接: {target_device_id}")
                                # 向发送方回复设备未找到
                                response = json.dumps({
                                    "type": "speak_response",
                                    "data": {"success": False, "message": f"Device not found or not connected: {target_device_id}", "connected_devices": list(connected_devices.keys())}
                                }, ensure_ascii=False)
                                await websocket.send(response)
                        
                        elif event_name == 'reconnect_server':
                            # 处理重连服务器指令
                            target_device_id = event_data.get('device_id', '')
                            
                            print("=" * 50)
                            print("🔄 收到设备重连指令！")
                            print(f"🔍 目标设备: {target_device_id}")
                            print("=" * 50)
                            
                            if target_device_id and target_device_id in connected_devices:
                                # 找到目标设备，转发重连消息
                                target_websocket = connected_devices[target_device_id]['websocket']
                                
                                # 构造转发消息
                                forward_message = json.dumps({
                                    "type": "reconnect_server",
                                    "data": {}
                                }, ensure_ascii=False)
                                
                                try:
                                    await target_websocket.send(forward_message)
                                    print(f"✅ 重连消息已转发到设备: {target_device_id}")
                                    print(f"📤 转发内容: {forward_message}")
                                    
                                    # 向发送方回复成功
                                    response = json.dumps({
                                        "type": "reconnect_response",
                                        "data": {"success": True, "message": "Reconnect command sent to device", "target_device": target_device_id}
                                    }, ensure_ascii=False)
                                    await websocket.send(response)
                                    print(f"📤 发送成功响应: {response}")
                                    
                                except Exception as e:
                                    print(f"❌ 转发重连消息失败: {e}")
                                    # 向发送方回复失败
                                    response = json.dumps({
                                        "type": "reconnect_response",
                                        "data": {"success": False, "message": f"Failed to send reconnect command: {str(e)}"}
                                    }, ensure_ascii=False)
                                    await websocket.send(response)
                            else:
                                print(f"❌ 目标设备未找到或未连接: {target_device_id}")
                                # 向发送方回复设备未找到
                                response = json.dumps({
                                    "type": "reconnect_response",
                                    "data": {"success": False, "message": f"Device not found or not connected: {target_device_id}", "connected_devices": list(connected_devices.keys())}
                                }, ensure_ascii=False)
                                await websocket.send(response)
                        
                        elif event_name == 'reboot_device':
                            # 处理设备重启指令
                            target_device_id = event_data.get('device_id', '')

                            print("=" * 50)
                            print("🔄 收到设备重启指令！")
                            print(f"🔍 目标设备: {target_device_id}")
                            print("=" * 50)

                            if target_device_id and target_device_id in connected_devices:
                                # 找到目标设备，转发消息
                                target_websocket = connected_devices[target_device_id]['websocket']

                                # 构造转发消息
                                forward_message = json.dumps({
                                    "type": "reboot_device",
                                    "data": {}
                                }, ensure_ascii=False)

                                try:
                                    await target_websocket.send(forward_message)
                                    print(f"✅ 重启消息已转发到设备: {target_device_id}")
                                    print(f"📤 转发内容: {forward_message}")

                                    # 向发送方回复成功
                                    response = json.dumps({
                                        "type": "reboot_response",
                                        "data": {"success": True, "message": "Reboot command sent to device", "target_device": target_device_id}
                                    }, ensure_ascii=False)
                                    await websocket.send(response)
                                    print(f"📤 发送成功响应: {response}")

                                except Exception as e:
                                    print(f"❌ 转发重启消息失败: {e}")
                                    # 向发送方回复失败
                                    response = json.dumps({
                                        "type": "reboot_response",
                                        "data": {"success": False, "message": f"Failed to send reboot command: {str(e)}"}
                                    }, ensure_ascii=False)
                                    await websocket.send(response)
                            else:
                                print(f"❌ 目标设备未找到或未连接: {target_device_id}")
                                # 向发送方回复设备未找到
                                response = json.dumps({
                                    "type": "reboot_response",
                                    "data": {"success": False, "message": f"Device not found or not connected: {target_device_id}", "connected_devices": list(connected_devices.keys())}
                                }, ensure_ascii=False)
                                await websocket.send(response)

                        elif event_name == 'get_all_devices':
                            # 处理设备状态查询请求
                            print("🔍 收到设备状态查询请求")
                            
                            devices_info = []
                            for device_id, info in connected_devices.items():
                                device_info = {
                                    'device_id': device_id,
                                    'device_name': info.get('device_name', 'Unknown'),
                                    'device_state': info.get('device_state', 'unknown'),
                                    'can_speak': info.get('can_speak', False),
                                    'voice_detected': info.get('voice_detected', False),
                                    'last_update': info.get('last_update', 'Never'),
                                    'connected': True
                                }
                                devices_info.append(device_info)
                            
                            # 发送设备列表响应
                            response = json.dumps({
                                "type": "device_list_response",
                                "data": {
                                    "devices": devices_info,
                                    "total_count": len(devices_info),
                                    "timestamp": datetime.now().isoformat()
                                }
                            }, ensure_ascii=False)
                            
                            await websocket.send(response)
                            print(f"📤 已发送设备列表响应: {len(devices_info)} 台设备")
                        
                        else:
                            print(f"⚠️ 未知的ESP32事件: {event_name}")
                    else:
                        print(f"⚠️ JSON消息缺少type字段: {message_data}")
                        
                except Exception as e:
                    print(f"❌ 解析ESP32 JSON消息失败: {e}")
            else:
                print(f"⚠️ 未知消息格式: {message}")
                
    except websockets.exceptions.ConnectionClosed:
        print(f"🔌 连接断开: {client_id}")
    except Exception as e:
        print(f"❌ 处理连接错误: {e}")
    finally:
        # 清理断开的设备
        cleanup_device(websocket)

def load_device_configs():
    """加载设备配置"""
    global device_configs
    try:
        if os.path.exists('devices_config.json'):
            with open('devices_config.json', 'r', encoding='utf-8') as f:
                device_configs = json.load(f)
            print(f"✅ 加载了 {len(device_configs)} 个设备配置")
    except Exception as e:
        print(f"❌ 加载配置失败: {e}")

def save_device_configs():
    """保存设备配置"""
    try:
        with open('devices_config.json', 'w', encoding='utf-8') as f:
            json.dump(device_configs, f, ensure_ascii=False, indent=2)
        print(f"💾 设备配置已保存，共 {len(device_configs)} 个设备")
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")

def save_runtime_data():
    """保存运行时数据供Flask服务器使用"""
    try:
        runtime_data = {
            'connected_devices': {},
            'device_configs': device_configs,
            'status': get_status(),
            'devices_list': get_devices_list(),
            'timestamp': datetime.now().isoformat()
        }

        # 转换connected_devices，移除websocket对象
        for device_id, device_info in connected_devices.items():
            runtime_data['connected_devices'][device_id] = {
                'device_name': device_info['device_info'].get('device_name', device_id),
                'device_type': device_info['device_info'].get('device_type', 'esp32'),
                'device_state': device_info.get('device_state', 'idle'),
                'capabilities': device_info['device_info'].get('capabilities', ''),
                'connect_time': device_info.get('connect_time'),
                'client_id': device_info['device_info'].get('client_id', '')
            }

        with open('runtime_data.json', 'w', encoding='utf-8') as f:
            json.dump(runtime_data, f, ensure_ascii=False, indent=2)

    except Exception as e:
        print(f"❌ 保存运行时数据失败: {e}")

def cleanup_device(websocket):
    """清理断开的设备"""
    for device_id, device_info in list(connected_devices.items()):
        if device_info.get('websocket') == websocket:
            del connected_devices[device_id]
            print(f"📱 设备断开: {device_id}")

            # 保存运行时数据
            save_runtime_data()
            break

def get_devices_list():
    """获取设备列表"""
    devices = []

    # 添加所有配置中的设备
    for device_id, config in device_configs.items():
        is_online = device_id in connected_devices

        if is_online:
            # 在线设备
            device_info = connected_devices[device_id]
            devices.append({
                'device_id': device_id,
                'device_name': device_info['device_info'].get('device_name', config.get('device_name', device_id)),
                'device_type': device_info['device_info'].get('device_type', config.get('device_type', 'esp32')),
                'device_state': device_info.get('device_state', 'unknown'),
                'capabilities': device_info['device_info'].get('capabilities', config.get('capabilities', '')),
                'online': True,
                'connect_time': device_info.get('connect_time'),
                'auto_registered': config.get('auto_registered', False)
            })
        else:
            # 离线设备
            devices.append({
                'device_id': device_id,
                'device_name': config.get('device_name', device_id),
                'device_type': config.get('device_type', 'esp32'),
                'device_state': 'offline',
                'capabilities': config.get('capabilities', ''),
                'online': False,
                'last_seen': config.get('last_seen', ''),
                'auto_registered': config.get('auto_registered', False)
            })

    return devices

def get_status():
    """获取系统状态"""
    online_devices = len(connected_devices)
    total_devices = len(device_configs)

    return {
        'server': 'ESP32 WebSocket Server',
        'connected_devices': online_devices,
        'total_devices': total_devices,
        'offline_devices': total_devices - online_devices,
        'timestamp': datetime.now().isoformat(),
        'status': 'running'
    }

async def main():
    """主函数"""
    host = '0.0.0.0'
    port = 15000

    # 加载配置
    load_device_configs()

    print("🚀 启动标准WebSocket服务器")
    print("=" * 50)
    print(f"📡 ESP32连接地址: ws://*************:{port}")
    print("💡 专门处理ESP32设备连接和消息")
    print("💡 支持标准JSON格式消息：{\"type\":\"event_name\",\"data\":{...}}")
    print()
    print("📋 支持的事件类型:")
    print("  - device_register: 设备注册")
    print("  - device_status: 设备状态更新")
    print("  - wake_device: 唤醒设备")
    print("  - speak_message: 让设备说话")
    print("  - reconnect_server: 重连服务器")
    print("  - reboot_device: 重启设备")
    print()
    print("🧪 测试命令:")
    print("  python test_wake_device.py")
    print("=" * 50)

    # 启动WebSocket服务器
    async with websockets.serve(handle_client, host, port):
        print(f"📡 WebSocket服务器启动在端口 {port}")
        print("🔄 实时状态监控已启用 - 设备状态变化将立即显示")
        
        # 保持服务器运行
        await asyncio.Future()  # 永远等待

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
