/**
 * ESP32 定时任务管理 - 前端JavaScript应用
 * 
 * 功能：
 * 1. ESP32设备状态监控
 * 2. 定时任务管理 (增删改查)
 * 3. 实时任务调度
 * 4. 自定义唤醒提示词
 */

class ESP32TimerApp {
    constructor() {
        this.config = null;
        this.tasks = [];
        this.currentTaskId = null;
        this.statusInterval = null;
        this.init();
    }

    // 初始化应用
    async init() {
        console.log('ESP32 Timer App initializing...');
        
        // 绑定事件监听器
        this.bindEventListeners();
        
        // 加载配置
        await this.loadConfig();
        
        // 初始化界面
        this.initializeInterface();
        
        // 加载任务列表
        await this.loadTasks();
        
        // 启动状态监控
        this.startStatusMonitoring();
        
        console.log('ESP32 Timer App initialized successfully');
    }

    // 绑定事件监听器
    bindEventListeners() {
        // 设备控制
        document.getElementById('wake-now').addEventListener('click', () => this.wakeDevice());
        document.getElementById('reboot-device').addEventListener('click', () => this.rebootDevice());
        document.getElementById('refresh-status').addEventListener('click', () => this.refreshDeviceStatus());
        
        // 定时器类型切换
        document.querySelectorAll('input[name="timer-type"]').forEach(radio => {
            radio.addEventListener('change', () => this.switchTimerType());
        });
        
        // 添加任务
        document.getElementById('add-timer').addEventListener('click', () => this.addTimer());
        
        // 刷新任务列表
        document.getElementById('refresh-tasks').addEventListener('click', () => this.loadTasks());
        
        // 模态框确认按钮
        document.getElementById('confirmButton').addEventListener('click', () => this.executeConfirmAction());
    }

    // 加载配置
    async loadConfig() {
        try {
            const response = await fetch('/api/config');
            this.config = await response.json();
            console.log('Config loaded:', this.config);
        } catch (error) {
            console.error('Failed to load config:', error);
            this.showMessage('配置加载失败', 'error');
        }
    }

    // 初始化界面
    initializeInterface() {
        if (this.config) {
            document.getElementById('device-ip').textContent = `${this.config.esp32_ip}:${this.config.esp32_port}`;
        }
        
        // 设置当前时间到绝对时间输入框
        const now = new Date();
        document.getElementById('abs-hour').value = now.getHours();
        document.getElementById('abs-minute').value = now.getMinutes();
        document.getElementById('abs-second').value = 0;
    }

    // 切换定时器类型
    switchTimerType() {
        const timerType = document.querySelector('input[name="timer-type"]:checked').value;
        const absolutePanel = document.getElementById('absolute-time-panel');
        const relativePanel = document.getElementById('relative-time-panel');
        const oneTimeTask = document.getElementById('one-time-task');
        
        if (timerType === 'absolute') {
            absolutePanel.style.display = 'block';
            relativePanel.style.display = 'none';
            oneTimeTask.checked = false;
        } else {
            absolutePanel.style.display = 'none';
            relativePanel.style.display = 'block';
            oneTimeTask.checked = true; // 相对时间默认为一次性任务
        }
    }

    // 启动设备状态监控
    startStatusMonitoring() {
        // 立即检查一次状态
        this.refreshDeviceStatus();
        
        // 每10秒检查一次状态
        this.statusInterval = setInterval(() => {
            this.refreshDeviceStatus();
        }, 10000);
    }

    // 刷新设备状态
    async refreshDeviceStatus() {
        try {
            const response = await fetch('/api/esp32/status');
            const result = await response.json();
            
            if (result.device_state) {
                this.updateDeviceStatus(result);
            } else {
                this.updateDeviceStatus({
                    device_state: 'unknown',
                    message: result.message || '设备离线'
                });
            }
        } catch (error) {
            console.error('Failed to refresh device status:', error);
            this.updateDeviceStatus({
                device_state: 'error',
                message: '连接失败'
            });
        }
    }

    // 更新设备状态显示
    updateDeviceStatus(status) {
        const deviceStatusElement = document.getElementById('device-status');
        const deviceStateElement = document.getElementById('device-state');
        const lastUpdateElement = document.getElementById('last-update');
        
        // 状态映射
        const statusMap = {
            'idle': { text: '空闲', class: 'status-idle' },
            'listening': { text: '监听中', class: 'status-listening' },
            'speaking': { text: '讲话中', class: 'status-speaking' },
            'error': { text: '错误', class: 'status-error' },
            'unknown': { text: '未知', class: 'status-unknown' }
        };
        
        const state = statusMap[status.device_state] || statusMap['unknown'];
        
        // 更新状态徽章
        deviceStateElement.textContent = state.text;
        deviceStateElement.className = `badge ${state.class}`;
        
        // 更新导航栏状态
        const navIcon = deviceStatusElement.querySelector('i');
        const navText = deviceStatusElement.querySelector('span') || deviceStatusElement;
        
        navIcon.className = `fas fa-circle ${state.class === 'status-idle' ? 'text-success' : 
                                            state.class === 'status-error' ? 'text-danger' : 'text-warning'}`;
        
        if (navText.tagName === 'SPAN') {
            navText.textContent = state.text;
        }
        
        // 更新最后更新时间
        lastUpdateElement.textContent = new Date().toLocaleTimeString();
    }

    // 唤醒设备
    async wakeDevice() {
        const message = document.getElementById('wake-message').value.trim();
        const force = document.getElementById('force-wake').checked;
        
        if (!message) {
            this.showMessage('请输入唤醒消息', 'error');
            return;
        }
        
        try {
            const response = await fetch('/api/esp32/wake', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: message,
                    force: force
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showMessage(`唤醒成功: ${result.message}`, 'success');
                // 立即刷新设备状态
                setTimeout(() => this.refreshDeviceStatus(), 1000);
            } else {
                this.showMessage(`唤醒失败: ${result.message}`, 'error');
            }
        } catch (error) {
            console.error('Wake device error:', error);
            this.showMessage('唤醒请求失败，请检查网络连接', 'error');
        }
    }

    // 重启设备
    async rebootDevice() {
        this.showConfirm('确认重启设备？', '设备将重启并断开连接约30秒', async () => {
            try {
                const response = await fetch('/api/esp32/reboot', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        confirm: true
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    this.showMessage('重启指令已发送，设备将在1秒后重启', 'success');
                    // 30秒后恢复状态监控
                    setTimeout(() => {
                        this.refreshDeviceStatus();
                    }, 30000);
                } else {
                    this.showMessage(`重启失败: ${result.message}`, 'error');
                }
            } catch (error) {
                console.error('Reboot device error:', error);
                this.showMessage('重启请求失败，请检查网络连接', 'error');
            }
        });
    }

    // 添加定时任务
    async addTimer() {
        const timerType = document.querySelector('input[name="timer-type"]:checked').value;
        const message = document.getElementById('timer-message').value.trim();
        const oneTime = document.getElementById('one-time-task').checked;
        
        if (!message) {
            this.showMessage('请输入唤醒消息', 'error');
            return;
        }
        
        let requestData = {
            message: message,
            one_time: oneTime
        };
        
        let url = '/api/timers';
        
        if (timerType === 'absolute') {
            // 绝对时间
            const hour = parseInt(document.getElementById('abs-hour').value);
            const minute = parseInt(document.getElementById('abs-minute').value);
            const second = parseInt(document.getElementById('abs-second').value);
            
            // 获取选中的重复日期
            const repeatDays = [];
            for (let i = 0; i <= 6; i++) {
                if (document.getElementById(`day-${i}`).checked) {
                    repeatDays.push(i);
                }
            }
            
            if (repeatDays.length === 0 && !oneTime) {
                this.showMessage('请至少选择一个重复日期', 'error');
                return;
            }
            
            requestData = {
                ...requestData,
                hour: hour,
                minute: minute,
                second: second,
                repeat_days: repeatDays
            };
        } else {
            // 相对时间
            const hours = parseInt(document.getElementById('rel-hours').value) || 0;
            const minutes = parseInt(document.getElementById('rel-minutes').value) || 0;
            const seconds = parseInt(document.getElementById('rel-seconds').value) || 0;
            
            if (hours === 0 && minutes === 0 && seconds === 0) {
                this.showMessage('请至少设置一个时间参数', 'error');
                return;
            }
            
            requestData = {
                ...requestData,
                hours: hours,
                minutes: minutes,
                seconds: seconds
            };
            
            url = '/api/timers/relative';
        }
        
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showMessage(`定时任务添加成功: ${result.message}`, 'success');
                // 清空表单
                this.resetForm();
                // 重新加载任务列表
                await this.loadTasks();
            } else {
                this.showMessage(`添加失败: ${result.message}`, 'error');
            }
        } catch (error) {
            console.error('Add timer error:', error);
            this.showMessage('添加定时任务失败，请检查网络连接', 'error');
        }
    }

    // 重置表单
    resetForm() {
        document.getElementById('timer-message').value = '定时提醒';
        document.getElementById('one-time-task').checked = false;
        
        // 重置相对时间
        document.getElementById('rel-hours').value = 0;
        document.getElementById('rel-minutes').value = 5;
        document.getElementById('rel-seconds').value = 0;
        
        // 重置绝对时间为当前时间
        const now = new Date();
        document.getElementById('abs-hour').value = now.getHours();
        document.getElementById('abs-minute').value = now.getMinutes();
        document.getElementById('abs-second').value = 0;
    }

    // 加载任务列表
    async loadTasks() {
        try {
            const response = await fetch('/api/timers');
            const result = await response.json();
            
            if (result.success) {
                this.tasks = result.tasks || [];
                this.renderTasks();
                this.updateTaskCount();
            } else {
                this.showMessage(`加载任务失败: ${result.message}`, 'error');
            }
        } catch (error) {
            console.error('Load tasks error:', error);
            this.showMessage('加载任务列表失败，请检查网络连接', 'error');
        }
    }

    // 渲染任务列表
    renderTasks() {
        const tasksList = document.getElementById('tasks-list');
        
        if (this.tasks.length === 0) {
            tasksList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-clock"></i>
                    <p>暂无定时任务</p>
                </div>
            `;
            return;
        }
        
        tasksList.innerHTML = this.tasks.map(task => this.createTaskHTML(task)).join('');
    }

    // 创建任务HTML
    createTaskHTML(task) {
        const timeStr = `${task.hour.toString().padStart(2, '0')}:${task.minute.toString().padStart(2, '0')}:${task.second.toString().padStart(2, '0')}`;
        
        // 重复日期文本
        const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        let repeatText = '';
        
        if (task.one_time) {
            repeatText = '一次性任务';
        } else if (task.repeat_days && task.repeat_days.length > 0) {
            if (task.repeat_days.length === 7) {
                repeatText = '每天';
            } else {
                repeatText = task.repeat_days.map(day => dayNames[day]).join(', ');
            }
        } else {
            repeatText = '相对时间任务';
        }
        
        // 任务状态
        const statusClass = task.enabled ? 'text-success' : 'text-muted';
        const statusText = task.enabled ? '启用' : '禁用';
        
        // 最后执行时间
        const lastRun = task.last_run ? new Date(task.last_run).toLocaleString() : '从未执行';
        
        return `
            <div class="task-item" data-task-id="${task.id}">
                <div class="task-time">${timeStr}</div>
                <div class="task-message">${task.message}</div>
                <div class="task-details">
                    <div class="row">
                        <div class="col-md-6">
                            <small><i class="fas fa-calendar-alt me-1"></i>${repeatText}</small>
                        </div>
                        <div class="col-md-6">
                            <small><i class="fas fa-info-circle me-1"></i>状态: <span class="${statusClass}">${statusText}</span></small>
                        </div>
                    </div>
                    <div class="row mt-1">
                        <div class="col-md-6">
                            <small><i class="fas fa-clock me-1"></i>最后执行: ${lastRun}</small>
                        </div>
                        <div class="col-md-6">
                            <small><i class="fas fa-plus me-1"></i>创建时间: ${new Date(task.created_at).toLocaleString()}</small>
                        </div>
                    </div>
                </div>
                <div class="task-actions">
                    <button class="btn btn-outline-${task.enabled ? 'warning' : 'success'} btn-sm" 
                            onclick="app.toggleTask('${task.id}', ${!task.enabled})">
                        <i class="fas fa-${task.enabled ? 'pause' : 'play'}"></i>
                        ${task.enabled ? '禁用' : '启用'}
                    </button>
                    <button class="btn btn-outline-primary btn-sm" onclick="app.editTask('${task.id}')">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="app.deleteTask('${task.id}')">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </div>
            </div>
        `;
    }

    // 更新任务数量
    updateTaskCount() {
        const taskCount = document.getElementById('task-count');
        taskCount.textContent = `${this.tasks.length} 个任务`;
    }

    // 切换任务状态
    async toggleTask(taskId, enabled) {
        try {
            const response = await fetch(`/api/timers/${taskId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    enabled: enabled
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showMessage(`任务${enabled ? '启用' : '禁用'}成功`, 'success');
                await this.loadTasks();
            } else {
                this.showMessage(`操作失败: ${result.message}`, 'error');
            }
        } catch (error) {
            console.error('Toggle task error:', error);
            this.showMessage('操作失败，请检查网络连接', 'error');
        }
    }

    // 编辑任务
    editTask(taskId) {
        const task = this.tasks.find(t => t.id === taskId);
        if (!task) return;
        
        // 这里可以实现编辑功能
        this.showMessage('编辑功能开发中...', 'info');
    }

    // 删除任务
    deleteTask(taskId) {
        const task = this.tasks.find(t => t.id === taskId);
        if (!task) return;
        
        this.showConfirm('确认删除任务？', `删除任务: ${task.message} (${task.hour.toString().padStart(2, '0')}:${task.minute.toString().padStart(2, '0')}:${task.second.toString().padStart(2, '0')})`, async () => {
            try {
                const response = await fetch(`/api/timers/${taskId}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    this.showMessage('任务删除成功', 'success');
                    await this.loadTasks();
                } else {
                    this.showMessage(`删除失败: ${result.message}`, 'error');
                }
            } catch (error) {
                console.error('Delete task error:', error);
                this.showMessage('删除失败，请检查网络连接', 'error');
            }
        });
    }

    // 显示消息
    showMessage(message, type = 'info') {
        const modal = new bootstrap.Modal(document.getElementById('messageModal'));
        const title = document.getElementById('messageModalTitle');
        const body = document.getElementById('messageModalBody');
        
        const typeMap = {
            'success': { title: '成功', class: 'text-success', icon: 'fas fa-check-circle' },
            'error': { title: '错误', class: 'text-danger', icon: 'fas fa-exclamation-circle' },
            'warning': { title: '警告', class: 'text-warning', icon: 'fas fa-exclamation-triangle' },
            'info': { title: '提示', class: 'text-info', icon: 'fas fa-info-circle' }
        };
        
        const config = typeMap[type] || typeMap['info'];
        
        title.innerHTML = `<i class="${config.icon} me-2 ${config.class}"></i>${config.title}`;
        body.innerHTML = `<p class="mb-0">${message}</p>`;
        
        modal.show();
    }

    // 显示确认对话框
    showConfirm(title, message, callback) {
        const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
        const body = document.getElementById('confirmModalBody');
        
        body.innerHTML = `
            <h6>${title}</h6>
            <p class="mb-0">${message}</p>
        `;
        
        // 保存回调函数
        this.confirmCallback = callback;
        
        modal.show();
    }

    // 执行确认操作
    executeConfirmAction() {
        if (this.confirmCallback) {
            this.confirmCallback();
            this.confirmCallback = null;
        }
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('confirmModal'));
        modal.hide();
    }
}

// 全局应用实例
let app;

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    app = new ESP32TimerApp();
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (app && app.statusInterval) {
        clearInterval(app.statusInterval);
    }
}); 