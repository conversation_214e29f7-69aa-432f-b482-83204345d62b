/** 
*    ===============================================================================
*    MCP Tools Connector for Xiaozhi AI Assistant - WebSocket Client Version
*   ===============================================================================
*
*    Copyright (c) 2025 PonYoung（旷）
*    All rights reserved.
*
*    Repository: https://github.com/onepy/Mcp_Pipe-Xiaozhi-All
*    Author: PonYoung（旷）
*    License: MIT License
*
*    This is open source software licensed under the MIT License.
*    See the LICENSE file in the project root for full license terms.
*
*    ===============================================================================
*/
#include "mcp_wake.h"
#include "application.h"
#include "mcp_server.h"
#include "websocket_config.h"
#include "board.h"
#include "system_info.h"
#include <esp_log.h>
#include <esp_wifi.h>
#include <esp_netif.h>
#include <esp_timer.h>
#include <cJSON.h>
#include <string.h>
#include <freertos/task.h>

static const char* TAG = "RemoteWakeClient";

namespace remote_wake {

RemoteWakeClient* RemoteWakeClient::instance_ = nullptr;

RemoteWakeClient::RemoteWakeClient() : 
    client_(nullptr), 
    server_url_(""),
    device_id_(""),
    device_name_(""),
    connected_(false),
    registered_(false),
    event_group_handle_(nullptr),
    heartbeat_task_(nullptr) {
    instance_ = this;
    
    // 创建事件组
    event_group_handle_ = xEventGroupCreate();
    
    // 从配置管理器获取服务器URL
    auto& config = websocket_config::WebSocketConfig::GetInstance();
    server_url_ = config.GetServerUrl();
    
    RegisterDeviceCommunicationTools();
}

RemoteWakeClient::~RemoteWakeClient() {
    Stop();
    
    if (event_group_handle_) {
        vEventGroupDelete(event_group_handle_);
    }
    
    if (client_) {
        delete client_;
    }
    
    instance_ = nullptr;
}

void RemoteWakeClient::RegisterDeviceCommunicationTools() {
    auto& mcp_server = McpServer::GetInstance();
    
    // 注册设备间通信工具
    mcp_server.AddTool("device.send_message",
        "Send a message to another ESP32 device through the server. "
        "Use this tool when the user wants to communicate with another device, "
        "such as 'tell device2 to start meeting' or 'notify living room device to play music'. "
        "Set 'force' to true when the user explicitly requests direct reply mode, "
        "using keywords like '直接发送', '直接回复', '直接告诉', 'directly send', etc.",
        PropertyList({
            Property("target_device", kPropertyTypeString),
            Property("message", kPropertyTypeString),
            Property("force", kPropertyTypeBoolean, false)
        }),
        [this](const PropertyList& properties) -> ReturnValue {
            std::string target_device = properties["target_device"].value<std::string>();
            std::string message = properties["message"].value<std::string>();
            bool force = properties["force"].value<bool>();
            
            ESP_LOGI(TAG, "MCP Tool: device.send_message called - Target: %s, Message: %s, Force: %s",
                     target_device.c_str(), message.c_str(), force ? "true" : "false");
            
            return SendMessageToDevice(target_device, message, force);
        });
    
    // 注册设备发现工具
    mcp_server.AddTool("device.discover",
        "Discover available ESP32 devices through the server. "
        "Use this tool to find out what devices are available for communication.",
        PropertyList(),
        [this](const PropertyList& properties) -> ReturnValue {
            ESP_LOGI(TAG, "MCP Tool: device.discover called");
            return DiscoverDevices();
        });
    
    // 注册设备状态查询工具
    mcp_server.AddTool("device.get_status",
        "Get the status of a specific ESP32 device through the server.",
        PropertyList({
            Property("target_device", kPropertyTypeString)
        }),
        [this](const PropertyList& properties) -> ReturnValue {
            std::string target_device = properties["target_device"].value<std::string>();
            ESP_LOGI(TAG, "MCP Tool: device.get_status called - Target: %s", target_device.c_str());
            return GetRemoteDeviceStatus(target_device);
        });
}

void RemoteWakeClient::SetServerUrl(const std::string& server_url) {
    server_url_ = server_url;
    ESP_LOGI(TAG, "Server URL set to: %s", server_url_.c_str());
}

void RemoteWakeClient::Start() {
    if (client_) {
        ESP_LOGW(TAG, "WebSocket client already started");
        return;
    }

    // 等待WiFi连接
    esp_netif_t* netif = esp_netif_get_handle_from_ifkey("WIFI_STA_DEF");
    if (!netif) {
        ESP_LOGE(TAG, "WiFi station interface not found");
        return;
    }

    esp_netif_ip_info_t ip_info;
    if (esp_netif_get_ip_info(netif, &ip_info) != ESP_OK || ip_info.ip.addr == 0) {
        ESP_LOGE(TAG, "WiFi not connected, cannot start WebSocket client");
        return;
    }

    // 生成设备ID（基于MAC地址）
    device_id_ = SystemInfo::GetMacAddress();
    
    ESP_LOGI(TAG, "WiFi connected, IP: " IPSTR ", Device ID: %s", 
             IP2STR(&ip_info.ip), device_id_.c_str());

    // 创建WebSocket客户端（传入实际连接的URL以选择正确的传输类型）
    client_ = Board::GetInstance().CreateWebSocket(server_url_);
    if (!client_) {
        ESP_LOGE(TAG, "Failed to create WebSocket client");
        return;
    }

    // 设置WebSocket回调
    client_->OnData([this](const char* data, size_t len, bool binary) {
        HandleWebSocketData(data, len, binary);
    });

    client_->OnDisconnected([this]() {
        HandleWebSocketDisconnected();
    });

    // 设置请求头
    client_->SetHeader("Device-Id", device_id_.c_str());
    client_->SetHeader("Client-Id", Board::GetInstance().GetUuid().c_str());
    client_->SetHeader("User-Agent", "ESP32-MCP-Client");

    ESP_LOGI(TAG, "Connecting to WebSocket server: %s", server_url_.c_str());
    
    // 连接到服务器
    if (!client_->Connect(server_url_.c_str())) {
        ESP_LOGE(TAG, "Failed to connect to WebSocket server");
        delete client_;
        client_ = nullptr;
        return;
    }

    ESP_LOGI(TAG, "WebSocket client connected successfully");
    
    // 注册状态变化回调，自动同步设备状态到服务器
    auto& app = Application::GetInstance();
    app.SetStateChangeCallback([this](DeviceState new_state) {
        // 在后台线程中发送状态更新，避免阻塞状态变化
        xTaskCreate([](void* arg) {
            auto* client = static_cast<RemoteWakeClient*>(arg);
            client->SendDeviceStatusUpdate();
            vTaskDelete(nullptr);
        }, "status_update", 4096, this, 5, nullptr);
    });
    
    // 手动触发连接事件（项目WebSocket封装可能不会自动触发）
    OnConnected();
}

void RemoteWakeClient::Stop() {
    if (heartbeat_task_) {
        vTaskDelete(heartbeat_task_);
        heartbeat_task_ = nullptr;
    }

    if (client_) {
        delete client_;
        client_ = nullptr;
        ESP_LOGI(TAG, "WebSocket client stopped");
    }
    
    connected_ = false;
    registered_ = false;
}

void RemoteWakeClient::HandleWebSocketData(const char* data, size_t len, bool binary) {
    if (!binary) {
        // 处理文本消息
        std::string message(data, len);
        ESP_LOGI(TAG, "Received message: %s", message.c_str());
        
        // 解析WebSocket JSON消息
        std::string event_name, event_data;
        if (!ParseWebSocketMessage(message, event_name, event_data)) {
            ESP_LOGW(TAG, "Failed to parse WebSocket message: %s", message.c_str());
            return;
        }
        
        ESP_LOGI(TAG, "Parsed event: %s, data: %s", event_name.c_str(), event_data.c_str());
        
        // 处理不同的事件
        if (event_name == "speak_message") {
            cJSON* json = cJSON_Parse(event_data.c_str());
            if (json) {
                cJSON* msg_item = cJSON_GetObjectItem(json, "message");
                cJSON* force_item = cJSON_GetObjectItem(json, "force");
                
                std::string msg = cJSON_IsString(msg_item) ? cJSON_GetStringValue(msg_item) : "";
                bool force = cJSON_IsBool(force_item) ? cJSON_IsTrue(force_item) : false;
                
                if (!msg.empty()) {
                    HandleSpeakMessage(msg, force);
                }
                cJSON_Delete(json);
            }
        } else if (event_name == "device_communication") {
            cJSON* json = cJSON_Parse(event_data.c_str());
            if (json) {
                cJSON* target_item = cJSON_GetObjectItem(json, "target_device");
                cJSON* msg_item = cJSON_GetObjectItem(json, "message");
                cJSON* force_item = cJSON_GetObjectItem(json, "force");
                
                std::string target = cJSON_IsString(target_item) ? cJSON_GetStringValue(target_item) : "";
                std::string msg = cJSON_IsString(msg_item) ? cJSON_GetStringValue(msg_item) : "";
                bool force = cJSON_IsBool(force_item) ? cJSON_IsTrue(force_item) : false;
                
                if (!target.empty() && !msg.empty()) {
                    HandleDeviceCommunication(target, msg, force);
                }
                cJSON_Delete(json);
            }
        } else if (event_name == "wake_device") {
            // 处理设备唤醒指令
            cJSON* json = cJSON_Parse(event_data.c_str());
            if (json) {
                cJSON* message_item = cJSON_GetObjectItem(json, "message");
                cJSON* force_item = cJSON_GetObjectItem(json, "force");
                
                std::string message = cJSON_IsString(message_item) ? cJSON_GetStringValue(message_item) : "";
                bool force = cJSON_IsBool(force_item) ? cJSON_IsTrue(force_item) : false;
                
                HandleWakeDevice(message, force);
                cJSON_Delete(json);
            }
        } else if (event_name == "device_register_response") {
            // 处理设备注册响应
            cJSON* json = cJSON_Parse(event_data.c_str());
            if (json) {
                cJSON* success_item = cJSON_GetObjectItem(json, "success");
                cJSON* message_item = cJSON_GetObjectItem(json, "message");
                
                bool success = cJSON_IsBool(success_item) ? cJSON_IsTrue(success_item) : false;
                std::string message = cJSON_IsString(message_item) ? cJSON_GetStringValue(message_item) : "";
                
                if (success) {
                    registered_ = true;
                    ESP_LOGI(TAG, "Device registration confirmed: %s", message.c_str());
                } else {
                    registered_ = false;
                    ESP_LOGE(TAG, "Device registration failed: %s", message.c_str());
                }
                cJSON_Delete(json);
            }
        } else if (event_name == "reconnect_server") {
            // 处理重连服务器指令
            ESP_LOGI(TAG, "Received reconnect server command");
            
            // 发送响应
            cJSON* response = cJSON_CreateObject();
            cJSON_AddBoolToObject(response, "success", true);
            cJSON_AddStringToObject(response, "message", "Reconnecting to server...");
            
            char* json_str = cJSON_PrintUnformatted(response);
            SendMessage("reconnect_response", std::string(json_str));
            cJSON_Delete(response);
            free(json_str);
            
            // 停止当前连接并重新连接
            Stop();
            
            // 延迟一下再重连
            vTaskDelay(pdMS_TO_TICKS(2000));
            Start();
            
        } else if (event_name == "device_status_request") {
            // 发送设备状态响应
            std::string status_json = GetDeviceStatusJson();
            SendMessage("device_status_response", status_json);
        } else if (event_name == "reboot_device") {
            // 处理设备重启指令
            ESP_LOGI(TAG, "Received reboot device command");

            // 发送响应
            cJSON* response = cJSON_CreateObject();
            cJSON_AddBoolToObject(response, "success", true);
            cJSON_AddStringToObject(response, "message", "Device will reboot in 1 second...");

            char* json_str = cJSON_PrintUnformatted(response);
            SendMessage("reboot_response", std::string(json_str));
            cJSON_Delete(response);
            free(json_str);

            // 延迟1秒后重启设备
            vTaskDelay(pdMS_TO_TICKS(1000));
            esp_restart();
        }
    }
}

void RemoteWakeClient::HandleWebSocketDisconnected() {
    ESP_LOGI(TAG, "WebSocket disconnected");
    OnDisconnected();
}

void RemoteWakeClient::OnConnected() {
    connected_ = true;
    ESP_LOGI(TAG, "WebSocket connected");
    
    // 启动心跳任务
    if (!heartbeat_task_) {
        xTaskCreate(HeartbeatTask, "heartbeat_task", 2048, this, 5, &heartbeat_task_);
        ESP_LOGI(TAG, "Heartbeat task started");
    }
    
    // 自动注册设备
    if (!device_name_.empty()) {
        RegisterDevice(device_id_, device_name_);
    }
    
    // 发送初始状态
    SendDeviceStatus("connected");
}

void RemoteWakeClient::OnDisconnected() {
    connected_ = false;
    registered_ = false;
    ESP_LOGI(TAG, "WebSocket connection lost");
    
    if (heartbeat_task_) {
        vTaskDelete(heartbeat_task_);
        heartbeat_task_ = nullptr;
    }
}



bool RemoteWakeClient::ParseWebSocketMessage(const std::string& raw_message, std::string& event_name, std::string& event_data) {
    if (raw_message.empty()) {
        return false;
    }

    // 解析标准JSON格式: {"type": "event_name", "data": {...}}
    cJSON* json = cJSON_Parse(raw_message.c_str());
    if (!json) {
        ESP_LOGW(TAG, "Failed to parse JSON message");
        return false;
    }
    
    cJSON* type_item = cJSON_GetObjectItem(json, "type");
    if (!type_item || !cJSON_IsString(type_item)) {
        ESP_LOGW(TAG, "Missing or invalid 'type' field");
        cJSON_Delete(json);
        return false;
    }
    
    event_name = cJSON_GetStringValue(type_item);
    
    cJSON* data_item = cJSON_GetObjectItem(json, "data");
    if (data_item) {
        if (cJSON_IsObject(data_item) || cJSON_IsArray(data_item)) {
            char* data_str = cJSON_PrintUnformatted(data_item);
            event_data = std::string(data_str);
            free(data_str);
        } else if (cJSON_IsString(data_item)) {
            event_data = cJSON_GetStringValue(data_item);
        } else {
            event_data = "{}";
        }
    } else {
        event_data = "{}";
    }
    
    cJSON_Delete(json);
    return true;
}

std::string RemoteWakeClient::BuildWebSocketMessage(const std::string& event_name, const std::string& data) {
    // 构造标准JSON格式: {"type": "event_name", "data": {...}}
    cJSON* root = cJSON_CreateObject();
    cJSON_AddStringToObject(root, "type", event_name.c_str());
    
    // 尝试解析data为JSON，如果失败则作为字符串处理
    cJSON* data_json = cJSON_Parse(data.c_str());
    if (data_json) {
        cJSON_AddItemToObject(root, "data", data_json);
    } else {
        cJSON_AddStringToObject(root, "data", data.c_str());
    }
    
    char* json_str = cJSON_PrintUnformatted(root);
    std::string result(json_str);
    
    cJSON_Delete(root);
    free(json_str);
    
    return result;
}

bool RemoteWakeClient::SendMessage(const std::string& event_name, const std::string& data) {
    if (!connected_ || !client_) {
        ESP_LOGW(TAG, "Cannot send message: not connected");
        return false;
    }
    
    std::string message = BuildWebSocketMessage(event_name, data);
    ESP_LOGI(TAG, "Sending message: %s", message.c_str());
    
    if (!client_->Send(message)) {
        ESP_LOGE(TAG, "Failed to send WebSocket message");
        return false;
    }
    
    return true;
}

bool RemoteWakeClient::RegisterDevice(const std::string& device_id, const std::string& device_name) {
    device_id_ = device_id;
    device_name_ = device_name;
    
    if (!connected_) {
        ESP_LOGW(TAG, "Cannot register device: not connected");
        return false;
    }
    
    cJSON* data = cJSON_CreateObject();
    cJSON_AddStringToObject(data, "device_id", device_id.c_str());
    cJSON_AddStringToObject(data, "device_name", device_name.c_str());
    cJSON_AddStringToObject(data, "device_type", "esp32");
    cJSON_AddStringToObject(data, "capabilities", "voice,display");
    
    char* json_str = cJSON_PrintUnformatted(data);
    bool success = SendMessage("device_register", std::string(json_str));
    
    if (success) {
        ESP_LOGI(TAG, "Device registration sent: ID=%s, Name=%s", device_id.c_str(), device_name.c_str());
        ESP_LOGI(TAG, "Waiting for server confirmation...");
    }
    
    cJSON_Delete(data);
    free(json_str);
    
    return success;
}

void RemoteWakeClient::SendDeviceStatus(const std::string& status) {
    if (!connected_ || !registered_) {
        return;
    }
    
    std::string status_json = GetDeviceStatusJson();
    SendMessage("device_status", status_json);
}

void RemoteWakeClient::HandleSpeakMessage(const std::string& message, bool force) {
    ESP_LOGI(TAG, "Handling speak message: %s (force: %s)", message.c_str(), force ? "true" : "false");
    
    auto& app = Application::GetInstance();
    auto current_state = app.GetDeviceState();
    
    bool can_speak = force || (current_state == kDeviceStateIdle || current_state == kDeviceStateListening);
    
    if (can_speak) {
        std::string final_message = message;
        
        // 如果是强制模式，包装消息让AI只回复指定内容
        if (force) {
            final_message = "请你只回复,,," + message + ",,,之间的内容，不要添加任何其他文字。示例：如果我说,,," + message + ",,,，你应该只回复：" + message;
            ESP_LOGI(TAG, "Force mode enabled, wrapped message: %s", final_message.c_str());
        }
        
        // 发送远程消息进行TTS播放
        app.SendRemoteMessage(final_message);
        
        // 发送响应到服务器
        cJSON* response = cJSON_CreateObject();
        cJSON_AddBoolToObject(response, "success", true);
        cJSON_AddStringToObject(response, "message", "Message processed successfully");
        cJSON_AddStringToObject(response, "device_state", GetDeviceStateString(current_state).c_str());
        cJSON_AddStringToObject(response, "original_message", message.c_str());
        cJSON_AddBoolToObject(response, "force_mode", force);
        
        char* json_str = cJSON_PrintUnformatted(response);
        SendMessage("speak_response", std::string(json_str));
        
        cJSON_Delete(response);
        free(json_str);
    } else {
        // 发送错误响应
        cJSON* response = cJSON_CreateObject();
        cJSON_AddBoolToObject(response, "success", false);
        cJSON_AddStringToObject(response, "message", "Device is busy, use force=true to override");
        cJSON_AddStringToObject(response, "device_state", GetDeviceStateString(current_state).c_str());
        
        char* json_str = cJSON_PrintUnformatted(response);
        SendMessage("speak_response", std::string(json_str));
        
        cJSON_Delete(response);
        free(json_str);
    }
}

void RemoteWakeClient::HandleWakeDevice(const std::string& message, bool force) {
    ESP_LOGI(TAG, "Handling wake device command: message=%s, force=%s", message.c_str(), force ? "true" : "false");
    
    auto& app = Application::GetInstance();
    auto current_state = app.GetDeviceState();
    
    bool can_wake = force || (current_state == kDeviceStateIdle || current_state == kDeviceStateSpeaking);
    
    if (can_wake) {
        // 主动触发唤醒
        ESP_LOGI(TAG, "Triggering device wake");
        
        // 如果有消息，则唤醒后播放消息
        if (!message.empty()) {
            std::string final_message = message;
            
            // 如果是强制模式，包装消息让AI只回复指定内容
            if (force) {
                final_message = "请你只回复,,," + message + ",,,之间的内容，不要添加任何其他文字。示例：如果我说,,," + message + ",,,，你应该只回复：" + message;
                ESP_LOGI(TAG, "Wake force mode enabled, wrapped message: %s", final_message.c_str());
            }
            
            app.SendRemoteMessage(final_message);
        } else {
            // 只是唤醒，不播放消息
            // 这里可以触发其他唤醒行为，比如显示提示、播放提示音等
            ESP_LOGI(TAG, "Device woken up without message");
        }
        
        // 发送成功响应
        cJSON* response = cJSON_CreateObject();
        cJSON_AddBoolToObject(response, "success", true);
        cJSON_AddStringToObject(response, "message", "Device woken up successfully");
        cJSON_AddStringToObject(response, "device_state", GetDeviceStateString(app.GetDeviceState()).c_str());
        cJSON_AddStringToObject(response, "original_message", message.c_str());
        cJSON_AddBoolToObject(response, "force_mode", force);
        
        char* json_str = cJSON_PrintUnformatted(response);
        SendMessage("wake_response", std::string(json_str));
        
        cJSON_Delete(response);
        free(json_str);
    } else {
        // 发送错误响应
        cJSON* response = cJSON_CreateObject();
        cJSON_AddBoolToObject(response, "success", false);
        cJSON_AddStringToObject(response, "message", "Device is busy, use force=true to override");
        cJSON_AddStringToObject(response, "device_state", GetDeviceStateString(current_state).c_str());
        
        char* json_str = cJSON_PrintUnformatted(response);
        SendMessage("wake_response", std::string(json_str));
        
        cJSON_Delete(response);
        free(json_str);
    }
}

void RemoteWakeClient::HandleDeviceCommunication(const std::string& target_device, const std::string& message, bool force) {
    ESP_LOGI(TAG, "Handling device communication: target=%s, message=%s", target_device.c_str(), message.c_str());
    
    // 通过服务器转发消息到目标设备
    cJSON* data = cJSON_CreateObject();
    cJSON_AddStringToObject(data, "source_device", device_id_.c_str());
    cJSON_AddStringToObject(data, "target_device", target_device.c_str());
    cJSON_AddStringToObject(data, "message", message.c_str());
    cJSON_AddBoolToObject(data, "force", force);
    
    char* json_str = cJSON_PrintUnformatted(data);
    SendMessage("device_message_forward", std::string(json_str));
    
    cJSON_Delete(data);
    free(json_str);
}

std::string RemoteWakeClient::GetDeviceStatusJson() {
    auto& app = Application::GetInstance();
    auto current_state = app.GetDeviceState();
    
    cJSON* status = cJSON_CreateObject();
    cJSON_AddStringToObject(status, "device_id", device_id_.c_str());
    cJSON_AddStringToObject(status, "device_name", device_name_.c_str());
    cJSON_AddStringToObject(status, "device_state", GetDeviceStateString(current_state).c_str());
    cJSON_AddNumberToObject(status, "state_code", static_cast<int>(current_state));
    cJSON_AddBoolToObject(status, "connected", connected_);
    cJSON_AddBoolToObject(status, "registered", registered_);
    cJSON_AddBoolToObject(status, "can_speak", current_state == kDeviceStateIdle || current_state == kDeviceStateListening);
    cJSON_AddBoolToObject(status, "voice_detected", app.IsVoiceDetected());
    cJSON_AddNumberToObject(status, "timestamp", esp_timer_get_time() / 1000000); // 转换为秒
    
    char* json_str = cJSON_PrintUnformatted(status);
    std::string result(json_str);
    
    cJSON_Delete(status);
    free(json_str);
    
    return result;
}

std::string RemoteWakeClient::GetDeviceStateString(DeviceState state) {
    switch (state) {
        case kDeviceStateStarting: return "starting";
        case kDeviceStateIdle: return "idle";
        case kDeviceStateConnecting: return "connecting";
        case kDeviceStateListening: return "listening";
        case kDeviceStateSpeaking: return "speaking";
        case kDeviceStateActivating: return "activating";
        case kDeviceStateWifiConfiguring: return "wifi_configuring";
        default: return "unknown";
    }
}

void RemoteWakeClient::SendDeviceStatusUpdate() {
    if (!connected_ || !registered_) {
        // 如果未连接或未注册，跳过状态更新
        return;
    }
    
    // 获取当前设备状态JSON
    std::string status_json = GetDeviceStatusJson();
    
    // 发送状态更新到服务器
    SendMessage("device_status", status_json);
    
    ESP_LOGI(TAG, "Auto-sent device status update: %s", status_json.c_str());
}

std::string RemoteWakeClient::SendMessageToDevice(const std::string& target_device, const std::string& message, bool force) {
    ESP_LOGI(TAG, "Sending message to device: %s", target_device.c_str());
    
    if (!connected_) {
        return "{\"success\": false, \"message\": \"Not connected to server\"}";
    }
    
    // 通过服务器转发消息
    HandleDeviceCommunication(target_device, message, force);
    
    // 返回成功响应（实际响应会通过WebSocket异步接收）
    cJSON* result = cJSON_CreateObject();
    cJSON_AddBoolToObject(result, "success", true);
    cJSON_AddStringToObject(result, "message", "Message sent through server");
    cJSON_AddStringToObject(result, "target_device", target_device.c_str());
    cJSON_AddStringToObject(result, "sent_message", message.c_str());
    cJSON_AddStringToObject(result, "method", "websocket_forward");
    
    char* json_str = cJSON_PrintUnformatted(result);
    std::string result_str(json_str);
    cJSON_Delete(result);
    free(json_str);
    
    return result_str;
}

std::string RemoteWakeClient::DiscoverDevices() {
    ESP_LOGI(TAG, "Discovering devices through server");
    
    if (!connected_) {
        return "{\"success\": false, \"message\": \"Not connected to server\"}";
    }
    
    // 请求服务器提供设备列表
    SendMessage("device_list_request", "{}");
    
    // 返回请求已发送的响应（实际设备列表会通过WebSocket异步接收）
    cJSON* result = cJSON_CreateObject();
    cJSON_AddBoolToObject(result, "success", true);
    cJSON_AddStringToObject(result, "message", "Device discovery request sent to server");
    cJSON_AddStringToObject(result, "method", "server_query");
    
    char* json_str = cJSON_PrintUnformatted(result);
    std::string result_str(json_str);
    cJSON_Delete(result);
    free(json_str);
    
    return result_str;
}

std::string RemoteWakeClient::GetRemoteDeviceStatus(const std::string& target_device) {
    ESP_LOGI(TAG, "Getting remote device status: %s", target_device.c_str());
    
    if (!connected_) {
        return "{\"success\": false, \"message\": \"Not connected to server\"}";
    }
    
    // 请求目标设备状态
    cJSON* request = cJSON_CreateObject();
    cJSON_AddStringToObject(request, "target_device", target_device.c_str());
    
    char* json_str = cJSON_PrintUnformatted(request);
    SendMessage("device_status_request", std::string(json_str));
    
    cJSON_Delete(request);
    free(json_str);
    
    // 返回请求已发送的响应
    cJSON* result = cJSON_CreateObject();
    cJSON_AddBoolToObject(result, "success", true);
    cJSON_AddStringToObject(result, "message", "Status request sent to server");
    cJSON_AddStringToObject(result, "target_device", target_device.c_str());
    cJSON_AddStringToObject(result, "method", "server_query");
    
    json_str = cJSON_PrintUnformatted(result);
    std::string result_str(json_str);
    cJSON_Delete(result);
    free(json_str);
    
    return result_str;
}

void RemoteWakeClient::SendHeartbeat() {
    if (connected_ && client_) {
        // 纯WebSocket协议，不需要心跳消息
        // 注释掉Socket.IO心跳格式: "2" (ping)
        // client_->Send("2");
        ESP_LOGD(TAG, "Heartbeat skipped (pure WebSocket protocol)");
    }
}

void RemoteWakeClient::HeartbeatTask(void* arg) {
    RemoteWakeClient* client = static_cast<RemoteWakeClient*>(arg);
    auto& config = websocket_config::WebSocketConfig::GetInstance();
    TickType_t delay = pdMS_TO_TICKS(config.GetHeartbeatIntervalMs());
    
    while (client->connected_) {
        client->SendHeartbeat();
        vTaskDelay(delay);
    }
    
    // 任务结束时清空句柄
    client->heartbeat_task_ = nullptr;
    vTaskDelete(nullptr);
}

} // namespace remote_wake 