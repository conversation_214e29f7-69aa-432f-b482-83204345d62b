#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP32设备模拟器
模拟ESP32设备连接到WebSocket服务器，用于测试和开发
支持连接到远程服务器 ws://aiws.huaichao.cc
"""

import asyncio
import websockets
import json
import time
import random
import argparse

class ESP32Simulator:
    def __init__(self, device_id=None, device_name=None, server_url="ws://aiws.huaichao.cc"):
        """
        初始化设备模拟器
        
        Args:
            device_id: 设备MAC地址，如果不提供则随机生成
            device_name: 设备名称
            server_url: WebSocket服务器地址
        """
        self.device_id = device_id or self.generate_mac_address()
        self.device_name = device_name or f"模拟设备_{self.device_id[-6:]}"
        self.server_url = server_url
        self.websocket = None
        self.device_state = "idle"
        self.can_speak = True
        self.is_connected = False
        self.last_heartbeat = time.time()
        
        print(f"🤖 ESP32设备模拟器初始化")
        print(f"   设备ID: {self.device_id}")
        print(f"   设备名: {self.device_name}")
        print(f"   服务器: {self.server_url}")
        print(f"   设备码: {self.get_device_code()}")

    def generate_mac_address(self):
        """生成随机MAC地址"""
        mac = [0x02, 0x00, 0x00,
               random.randint(0x00, 0x7f),
               random.randint(0x00, 0xff),
               random.randint(0x00, 0xff)]
        return ':'.join(map(lambda x: "%02x" % x, mac)).upper()

    def get_device_code(self):
        """获取6位设备码（MAC地址后6位）"""
        return self.device_id.replace(':', '').replace('-', '')[-6:].upper()

    async def connect(self):
        """连接到WebSocket服务器"""
        try:
            print(f"🔗 正在连接到服务器: {self.server_url}")
            self.websocket = await websockets.connect(self.server_url)
            self.is_connected = True
            print(f"✅ 连接成功")
            
            # 发送设备注册消息
            await self.register_device()
            
            # 启动心跳和消息处理
            await asyncio.gather(
                self.heartbeat_loop(),
                self.message_handler(),
                self.simulate_device_behavior()
            )
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            self.is_connected = False

    async def register_device(self):
        """注册设备到服务器"""
        register_message = {
            "type": "device_register",
            "data": {
                "device_id": self.device_id,
                "device_name": self.device_name,
                "device_type": "esp32",
                "firmware_version": "1.0.0",
                "device_state": self.device_state,
                "can_speak": self.can_speak
            }
        }
        
        await self.send_message(register_message)
        print(f"📝 设备注册消息已发送")

    async def send_message(self, message):
        """发送消息到服务器"""
        if self.websocket and self.is_connected:
            try:
                await self.websocket.send(json.dumps(message, ensure_ascii=False))
            except Exception as e:
                print(f"❌ 发送消息失败: {e}")
                self.is_connected = False

    async def heartbeat_loop(self):
        """心跳循环"""
        while self.is_connected:
            try:
                heartbeat_message = {
                    "type": "heartbeat",
                    "data": {
                        "device_id": self.device_id,
                        "device_state": self.device_state,
                        "can_speak": self.can_speak,
                        "timestamp": time.time()
                    }
                }
                
                await self.send_message(heartbeat_message)
                self.last_heartbeat = time.time()
                
                # 每30秒发送一次心跳
                await asyncio.sleep(30)
                
            except Exception as e:
                print(f"❌ 心跳发送失败: {e}")
                break

    async def message_handler(self):
        """处理服务器消息"""
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    await self.handle_server_message(data)
                except json.JSONDecodeError:
                    print(f"⚠️ 收到无效JSON消息: {message}")
                except Exception as e:
                    print(f"❌ 处理消息失败: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            print("🔌 WebSocket连接已关闭")
            self.is_connected = False
        except Exception as e:
            print(f"❌ 消息处理异常: {e}")
            self.is_connected = False

    async def handle_server_message(self, data):
        """处理服务器消息"""
        message_type = data.get("type")
        message_data = data.get("data", {})
        
        if message_type == "speak_message":
            await self.handle_speak_message(message_data)
        elif message_type == "device_control":
            await self.handle_device_control(message_data)
        elif message_type == "ping":
            await self.handle_ping(message_data)
        elif message_type == "device_register_response":
            await self.handle_register_response(message_data)
        else:
            print(f"📨 收到未知消息类型: {message_type}")

    async def handle_speak_message(self, data):
        """处理TTS消息"""
        message = data.get("message", "")
        live_id = data.get("live_id", "")
        
        print(f"🔊 收到TTS消息: {message[:50]}...")
        
        # 模拟TTS播放过程
        self.device_state = "speaking"
        self.can_speak = False
        
        # 发送开始播放响应
        response = {
            "type": "speak_response",
            "data": {
                "device_id": self.device_id,
                "success": True,
                "message": "开始播放"
            }
        }
        await self.send_message(response)
        
        # 模拟播放时间（根据消息长度）
        play_duration = max(2, len(message) * 0.1)  # 最少2秒
        print(f"⏳ 模拟播放 {play_duration:.1f} 秒...")
        
        await asyncio.sleep(play_duration)
        
        # 播放完成
        self.device_state = "idle"
        self.can_speak = True
        
        print(f"✅ TTS播放完成")

    async def handle_device_control(self, data):
        """处理设备控制消息"""
        command = data.get("command")
        
        if command == "wake":
            print("⏰ 收到唤醒命令")
            self.device_state = "idle"
            self.can_speak = True
        elif command == "test":
            print("🧪 收到测试命令")
            await self.handle_speak_message({"message": "设备测试成功", "live_id": ""})
        else:
            print(f"❓ 收到未知控制命令: {command}")

    async def handle_ping(self, data):
        """处理ping消息"""
        pong_message = {
            "type": "pong",
            "data": {
                "device_id": self.device_id,
                "timestamp": time.time()
            }
        }
        await self.send_message(pong_message)

    async def simulate_device_behavior(self):
        """模拟设备行为"""
        while self.is_connected:
            try:
                # 随机模拟一些设备状态变化
                await asyncio.sleep(random.randint(60, 180))  # 1-3分钟
                
                if random.random() < 0.1:  # 10%概率
                    print("🔄 模拟设备状态变化...")
                    old_state = self.device_state
                    
                    # 模拟短暂的连接状态
                    self.device_state = "connecting"
                    await asyncio.sleep(2)
                    
                    # 恢复原状态
                    self.device_state = old_state
                    print(f"✅ 设备状态恢复: {self.device_state}")
                    
            except Exception as e:
                print(f"❌ 设备行为模拟异常: {e}")
                break

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="ESP32设备模拟器")
    parser.add_argument("--device-id", help="设备MAC地址")
    parser.add_argument("--device-name", help="设备名称")
    parser.add_argument("--server", default="ws://127.0.0.1:15000", help="WebSocket服务器地址")
    
    args = parser.parse_args()
    
    print("🤖 ESP32设备模拟器")
    print("=" * 50)
    
    simulator = ESP32Simulator(
        device_id=args.device_id,
        device_name=args.device_name,
        server_url=args.server
    )
    
    try:
        asyncio.run(simulator.connect())
    except KeyboardInterrupt:
        print("\n👋 设备模拟器已停止")
    except Exception as e:
        print(f"❌ 设备模拟器异常: {e}")

if __name__ == "__main__":
    main()
