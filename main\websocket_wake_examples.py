#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket服务器端控制示例
展示如何通过WebSocket主动唤醒和控制ESP32设备
"""

import asyncio
import websockets
import json
from datetime import datetime

class DeviceController:
    def __init__(self):
        self.connected_devices = {}
    
    async def send_wake_device(self, device_id, message="", force=False):
        """
        主动唤醒设备
        
        Args:
            device_id: 设备ID (如: "30:ed:a0:23:82:ec")
            message: 唤醒后要说的内容 (可选)
            force: 是否强制唤醒 (默认False)
        """
        if device_id not in self.connected_devices:
            print(f"❌ 设备 {device_id} 未连接")
            return False
        
        websocket = self.connected_devices[device_id]['websocket']
        
        # 构造唤醒消息
        wake_data = {
            "message": message,
            "force": force
        }
        
        # Socket.IO格式: 42["wake_device", {...}]
        wake_message = f'42["wake_device",{json.dumps(wake_data, ensure_ascii=False)}]'
        
        try:
            await websocket.send(wake_message)
            print(f"✅ 唤醒消息已发送到设备 {device_id}")
            print(f"📤 消息内容: {wake_message}")
            return True
        except Exception as e:
            print(f"❌ 发送唤醒消息失败: {e}")
            return False
    
    async def send_speak_message(self, device_id, message, force=False):
        """
        让设备说话
        
        Args:
            device_id: 设备ID
            message: 要说的内容
            force: 是否强制说话
        """
        if device_id not in self.connected_devices:
            print(f"❌ 设备 {device_id} 未连接")
            return False
        
        websocket = self.connected_devices[device_id]['websocket']
        
        speak_data = {
            "message": message,
            "force": force
        }
        
        speak_message = f'42["speak_message",{json.dumps(speak_data, ensure_ascii=False)}]'
        
        try:
            await websocket.send(speak_message)
            print(f"✅ 说话消息已发送到设备 {device_id}")
            print(f"📤 消息内容: {speak_message}")
            return True
        except Exception as e:
            print(f"❌ 发送说话消息失败: {e}")
            return False
    
    async def send_device_communication(self, source_device_id, target_device_name, message, force=False):
        """
        设备间通信
        
        Args:
            source_device_id: 源设备ID (通过哪个设备发送)
            target_device_name: 目标设备名称
            message: 消息内容
            force: 是否强制发送
        """
        if source_device_id not in self.connected_devices:
            print(f"❌ 源设备 {source_device_id} 未连接")
            return False
        
        websocket = self.connected_devices[source_device_id]['websocket']
        
        comm_data = {
            "target_device": target_device_name,
            "message": message,
            "force": force
        }
        
        comm_message = f'42["device_communication",{json.dumps(comm_data, ensure_ascii=False)}]'
        
        try:
            await websocket.send(comm_message)
            print(f"✅ 设备通信消息已发送")
            print(f"📤 从 {source_device_id} 发送到 {target_device_name}: {message}")
            return True
        except Exception as e:
            print(f"❌ 发送设备通信消息失败: {e}")
            return False
    
    async def request_device_status(self, device_id):
        """
        请求设备状态

        Args:
            device_id: 设备ID
        """
        if device_id not in self.connected_devices:
            print(f"❌ 设备 {device_id} 未连接")
            return False

        websocket = self.connected_devices[device_id]['websocket']

        status_message = f'42["device_status_request",{{}}]'

        try:
            await websocket.send(status_message)
            print(f"✅ 状态请求已发送到设备 {device_id}")
            return True
        except Exception as e:
            print(f"❌ 发送状态请求失败: {e}")
            return False

    async def reboot_device(self, device_id):
        """
        重启设备

        Args:
            device_id: 设备ID (如: "30:ed:a0:23:82:ec")
        """
        if device_id not in self.connected_devices:
            print(f"❌ 设备 {device_id} 未连接")
            return False

        websocket = self.connected_devices[device_id]['websocket']

        # Socket.IO格式: 42["reboot_device", {}]
        reboot_message = f'42["reboot_device",{{}}]'

        try:
            await websocket.send(reboot_message)
            print(f"✅ 重启指令已发送到设备 {device_id}")
            print(f"📤 消息内容: {reboot_message}")
            print(f"⚠️ 设备将在1秒后重启，连接将断开")
            return True
        except Exception as e:
            print(f"❌ 发送重启指令失败: {e}")
            return False
    
    def register_device(self, device_id, websocket, device_info):
        """注册设备"""
        self.connected_devices[device_id] = {
            'websocket': websocket,
            'info': device_info,
            'connect_time': datetime.now()
        }
        print(f"📱 设备已注册: {device_id}")
    
    def unregister_device(self, device_id):
        """注销设备"""
        if device_id in self.connected_devices:
            del self.connected_devices[device_id]
            print(f"📱 设备已注销: {device_id}")
    
    def list_devices(self):
        """列出所有连接的设备"""
        print("📋 当前连接的设备:")
        for device_id, info in self.connected_devices.items():
            device_name = info['info'].get('device_name', 'Unknown')
            connect_time = info['connect_time'].strftime('%H:%M:%S')
            print(f"  - {device_id} ({device_name}) - 连接时间: {connect_time}")
        return list(self.connected_devices.keys())

# 使用示例
async def demo_wake_device():
    """演示如何唤醒设备"""
    controller = DeviceController()
    
    # 假设设备已连接，这里手动模拟
    device_id = "30:ed:a0:23:82:ec"
    
    print("🎯 设备控制演示")
    print("=" * 50)
    
    # 示例1: 简单唤醒设备
    print("示例1: 简单唤醒设备")
    await controller.send_wake_device(device_id)
    
    await asyncio.sleep(2)
    
    # 示例2: 唤醒并说话
    print("示例2: 唤醒并说话")
    await controller.send_wake_device(device_id, "你好，我被远程唤醒了！")
    
    await asyncio.sleep(2)
    
    # 示例3: 强制唤醒（即使设备忙碌）
    print("示例3: 强制唤醒")
    await controller.send_wake_device(device_id, "这是强制唤醒的消息", force=True)
    
    await asyncio.sleep(2)
    
    # 示例4: 让设备说话（不唤醒）
    print("示例4: 让设备说话")
    await controller.send_speak_message(device_id, "这是一条说话消息")
    
    await asyncio.sleep(2)
    
    # 示例5: 设备间通信
    print("示例5: 设备间通信")
    await controller.send_device_communication(device_id, "小灵17号", "你好，我是16号设备")
    
    await asyncio.sleep(2)
    
    # 示例6: 请求设备状态
    print("示例6: 请求设备状态")
    await controller.request_device_status(device_id)

    await asyncio.sleep(2)

    # 示例7: 重启设备
    print("示例7: 重启设备")
    print("⚠️ 注意：这将重启设备，请确认后再执行")
    # await controller.reboot_device(device_id)  # 取消注释以执行重启

if __name__ == "__main__":
    print("📋 WebSocket设备控制示例")
    print("支持的控制命令:")
    print("1. wake_device - 主动唤醒设备")
    print("2. speak_message - 让设备说话")
    print("3. device_communication - 设备间通信")
    print("4. device_status_request - 请求设备状态")
    print("5. reboot_device - 重启设备")
    print()
    
    # 注意: 这只是演示代码，实际使用需要集成到WebSocket服务器中
    # asyncio.run(demo_wake_device()) 