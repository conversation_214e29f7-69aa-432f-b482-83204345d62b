#ifndef _BOARD_CONFIG_H_
#define _BOARD_CONFIG_H_

#include <driver/gpio.h>
#include "es8311_codec.h"

#define AUDIO_INPUT_SAMPLE_RATE  24000
#define AUDIO_OUTPUT_SAMPLE_RATE 24000

// ES8311音频编解码器配置
#define AUDIO_I2S_GPIO_MCLK GPIO_NUM_3   // MCLK
#define AUDIO_I2S_GPIO_BCLK GPIO_NUM_46  // SCLK
#define AUDIO_I2S_GPIO_WS   GPIO_NUM_10  // LRCK
#define AUDIO_I2S_GPIO_DIN  GPIO_NUM_9  // 从ES8311到ESP32
#define AUDIO_I2S_GPIO_DOUT GPIO_NUM_11   // 从ESP32到ES8311

#define AUDIO_CODEC_PA_PIN       GPIO_NUM_12  // 功放使能
#define AUDIO_CODEC_I2C_SDA_PIN  GPIO_NUM_18  // ES8311 I2C SDA
#define AUDIO_CODEC_I2C_SCL_PIN  GPIO_NUM_8   // ES8311 I2C SCL
#define AUDIO_CODEC_ES8311_ADDR  ES8311_CODEC_DEFAULT_ADDR

// 显示屏OLED配置 (SSD1306)
#define DISPLAY_WIDTH   128
#define DISPLAY_HEIGHT  64
#define DISPLAY_MIRROR_X true
#define DISPLAY_MIRROR_Y true

// 按钮和LED配置
#define BUILTIN_LED_GPIO        GPIO_NUM_2  // LED引脚
#define BOOT_BUTTON_GPIO        GPIO_NUM_0

// 4G模块预留
#define ML307_TX_PIN GPIO_NUM_17
#define ML307_RX_PIN GPIO_NUM_18

#endif // _BOARD_CONFIG_H_ 